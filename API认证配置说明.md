# API 认证机制说明

### 1. 认证参数
采集中心调用接口时需要在HTTP头中包含以下参数：

- **X-API-Key**: API密钥，固定值：`collection_center_api_key`
- **X-Timestamp**: 时间戳（毫秒），用于防止重放攻击
- **X-Nonce**: 随机字符串，增加签名复杂度
- **X-Signature**: 签名，用于验证请求完整性

### 2. 签名算法
签名生成步骤：
1. 构建签名字符串：`METHOD + URI + API_KEY + TIMESTAMP + NONCE + SECRET`
2. 如果有查询参数，按字母顺序添加：`KEY1 + VALUE1 + KEY2 + VALUE2`
3. 对签名字符串进行MD5加密
4. 转换为大写十六进制字符串

### 3. 签名示例
```
请求方法: POST
请求URI: /platform-api/pro/chk-attach-url/receive-push-data
API Key: collection_center_api_key
时间戳: 1691136000000
随机数: abc123
密钥: dahe_collection_center_2025

签名字符串: POST/platform-api/pro/chk-attach-url/receive-push-datacollection_center_api_key1691136000000abc123dahe_collection_center_2025
MD5结果: A1B2C3D4E5F6789012345678901234AB
```

## 接口调用示例

### CURL示例
```bash
curl -X POST "http://localhost:8080/pro/chk-attach-url/receive-push-data" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: collection_center_api_key" \
  -H "X-Timestamp: 1691136000000" \
  -H "X-Nonce: abc123" \
  -H "X-Signature: A1B2C3D4E5F6789012345678901234AB" \
  -d '{"data": "推送的JSON数据"}'
```

### Java示例
```java
import java.security.MessageDigest;
import java.util.HashMap;
import java.util.Map;

public class ApiClient {
    private static final String API_KEY = "collection_center_api_key";
    private static final String SECRET = "dahe_collection_center_2025";
    
    public void callPushDataApi(String data) {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String nonce = generateNonce();
        String signature = generateSignature("POST", "/platform-api/pro/chk-attach-url/receive-push-data", 
                                           API_KEY, timestamp, nonce);
        
        Map<String, String> headers = new HashMap<>();
        headers.put("X-API-Key", API_KEY);
        headers.put("X-Timestamp", timestamp);
        headers.put("X-Nonce", nonce);
        headers.put("X-Signature", signature);
        
        // 发送HTTP请求...
    }
    
    private String generateSignature(String method, String uri, String apiKey, 
                                   String timestamp, String nonce) {
        String signStr = method + uri + apiKey + timestamp + nonce + SECRET;
        return md5(signStr).toUpperCase();
    }
    
    private String generateNonce() {
        return java.util.UUID.randomUUID().toString().replace("-", "");
    }
    
    private String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(input.getBytes("UTF-8"));
            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
```

### Python示例
```python
import hashlib
import time
import uuid
import requests

class ApiClient:
    API_KEY = "collection_center_api_key"
    SECRET = "dahe_collection_center_2025"
    BASE_URL = "http://localhost:8080"
    
    def call_push_data_api(self, data):
        timestamp = str(int(time.time() * 1000))
        nonce = str(uuid.uuid4()).replace('-', '')
        signature = self.generate_signature("POST", "/platform-api/pro/chk-attach-url/receive-push-data", 
                                          self.API_KEY, timestamp, nonce)
        
        headers = {
            "Content-Type": "application/json",
            "X-API-Key": self.API_KEY,
            "X-Timestamp": timestamp,
            "X-Nonce": nonce,
            "X-Signature": signature
        }
        
        response = requests.post(
            f"{self.BASE_URL}/pro/chk-attach-url/receive-push-data",
            headers=headers,
            json={"data": data}
        )
        
        return response.json()
    
    def generate_signature(self, method, uri, api_key, timestamp, nonce):
        sign_str = method + uri + api_key + timestamp + nonce + self.SECRET
        return hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()
```

## 安全特性

### 1. 时间戳验证
- 防止重放攻击
- 默认5分钟超时
- 可通过配置调整超时时间

### 2. 签名验证
- 确保请求完整性
- 防止数据篡改
- 使用MD5加密算法

### 3. API Key验证
- 验证调用方身份
- 可配置多个有效的API Key
- 支持API Key管理

### 4. 随机数验证
- 增加签名复杂度
- 防止签名被破解

## 错误响应

### 认证失败响应
```json
{
  "code": 401,
  "msg": "API认证失败",
  "data": null
}
```

### 常见错误原因
1. **API认证参数不完整**: 缺少必要的认证头
2. **无效的API Key**: API Key不在允许列表中
3. **请求时间戳过期**: 请求时间与服务器时间差异过大
4. **签名验证失败**: 签名计算错误或参数不匹配

## 配置管理建议

### 1. 生产环境配置
- 使用强密码作为SECRET
- 定期更换API Key
- 设置合适的超时时间
- 启用HTTPS传输

### 2. 监控告警
- 记录认证失败日志
- 监控异常请求频率
- 设置告警机制

### 3. 扩展功能
- 支持多个API Key管理
- 实现API Key的启用/禁用
- 添加请求频率限制
- 实现IP白名单功能
