package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;

import java.util.Date;

/**
 * 文章原文实体类
 * 与t_article表一一对应，存储原始采集内容
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@TableName("t_article_content")
public class ArticleContent {

    @IsKey
    @TableId
    @Column("id")
    @ColumnComment("主键ID，与t_article表ID一一对应")
    private Long id;

    @Column("website_id")
    @ColumnComment("关联的网站ID")
    private Long websiteId;

    @Column(name = "origin_title", type = MySqlTypeConstant.TEXT)
    @ColumnComment("原始HTML标题内容")
    private String originTitle;

    @Column(name = "origin_content", type = MySqlTypeConstant.LONGTEXT)
    @ColumnComment("原始HTML内容")
    private String originContent;

    @Column(name = "create_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("创建时间")
    @DefaultValue("CURRENT_TIMESTAMP")
    private Date createTime;

    @Column(name = "update_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("更新时间")
    @IgnoreUpdate
    @DefaultValue("NULL ON UPDATE CURRENT_TIMESTAMP")
    private Date updateTime;
} 