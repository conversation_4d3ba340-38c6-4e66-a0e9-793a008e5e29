package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsAutoIncrement;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsKey;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Map;

/**
 *
 */
@Data
@TableName("t_operate_log")
public class OperateLog {

    @TableId(type = IdType.AUTO)
    @IsKey
    @IsAutoIncrement
    private Integer id;
    /**
     * 用户编号
     */
    @Column(value = "user_id")
    private Integer userId;
    /**
     * 用户类型
     */
    @Column(value = "user_name")
    private String userName;

    /**
     * 操作模块
     */
    @Column(value = "module", type = MySqlTypeConstant.TEXT)
    private String module;

    /**
     * 操作名
     */
    @Column(value = "method_name", type = MySqlTypeConstant.TEXT)
    private String methodName;

    /**
     * 操作分类
     */
    @Column(value = "type")
    private Integer type;

    /**
     * 操作明细
     */
    @Column(value = "content", type = MySqlTypeConstant.TEXT)
    private String content;

    /**
     * 拓展字段
     */
    @Column(value = "exts", type = MySqlTypeConstant.TEXT)
    private Map<String, Object> exts;

    /**
     * 请求方法名
     */
    @Column(value = "request_method", type = MySqlTypeConstant.TEXT)
    private String requestMethod;

    /**
     * 请求地址
     */
    @Column(value = "request_url", type = MySqlTypeConstant.TEXT)
    private String requestUrl;

    /**
     * 用户 IP
     */
    @Column(value = "user_ip", type = MySqlTypeConstant.TEXT)
    private String userIp;

    /**
     * 浏览器 UserAgent
     */
    @Column(value = "user_agent", type = MySqlTypeConstant.TEXT)
    private String userAgent;

    /**
     * Java 方法名
     */
    @Column(value = "java_method", type = MySqlTypeConstant.TEXT)
    private String javaMethod;

    /**
     * Java 方法的参数
     */
    @Column(value = "java_method_args", type = MySqlTypeConstant.LONGTEXT)
    private String javaMethodArgs;

    /**
     * 开始时间
     */
    @Column(name = "start_time", comment = "开始时间", type = MySqlTypeConstant.DATETIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 执行时长，单位：毫秒
     */
    @Column(value = "duration")
    private Integer duration;

    /**
     * 结果码
     */
    @Column(value = "result_code")
    private Integer resultCode;

    /**
     * 结果提示
     */
    @Column(value = "result_msg", type = MySqlTypeConstant.TEXT)
    private String resultMsg;

    /**
     * 结果数据
     */
    @Column(value = "result_data", type = MySqlTypeConstant.TEXT)
    private String resultData;

}