package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.ColumnComment;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsAutoIncrement;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsKey;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-07-08
 */
@Data
@TableName("t_website_access_record")
public class WebsiteAccessRecord {

    @IsKey
    @IsAutoIncrement
    @Column("id")
    @ColumnComment("ID")
    private Integer id;

    @Column(value = "web_id", defaultValue = "0")
    @ColumnComment("网站Id")
    private Integer webId;

    @Column(value = "web_name")
    @ColumnComment("网站名称")
    private String webName;

    @Column("web_url")
    @ColumnComment("网站地址")
    private String webUrl;

    /**
     * 访问耗时（毫秒）
     */
    @Column(name = "access_time_consuming", defaultValue = "0", comment = "访问耗时（毫秒）")
    private Double accessTimeConsuming;

    /**
     * 返回状态码
     */
    @Column(name = "http_code", comment = "状态码，200表示成功", defaultValue = "200")
    private Integer httpCode;

    /**
     * 是否访问正常
     */
    @Column(name = "success", comment = "是否访问正常 0否1是", defaultValue = "0")
    private Integer success;

    /**
     * 类型
     */
    @Column(name = "type", comment = "类型：0内链，1外链", defaultValue = "0")
    private Integer type;

    /**
     * 内容类型
     */
    @Column(name = "content_type", comment = "内容类型", defaultValue = "0")
    private String contentType;

    @Column(name = "link_url", comment = "链接地址")
    private String linkUrl;

    @Column(name = "parent_url", comment = "父链接地址")
    private String parentUrl;

    @Column(name = "parent_web_code", comment = "父链接网站源码：<html> .... <html>")
    private String parentWebCode;

    @Column(name = "parent_pub_time", comment = "父链接发布时间", type = MySqlTypeConstant.DATETIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date parentPubTime;


    @Column(name = "check_time", comment = "访问时间/检查时间", type = MySqlTypeConstant.DATETIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;


    @Column(name = "create_time", comment = "添加时间", type = MySqlTypeConstant.DATETIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

}
