package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 附件检查实体类
 * 对应原型图功能需求
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@TableName("t_attachment")
@Schema(name = "附件检查", description = "网站附件检查信息")
public class Attachment {

    @IsKey
    @IsAutoIncrement
    @Column("id")
    @ColumnComment("编号")
    private Long id;

    @Column("website_id")
    @ColumnComment("网站ID")
    private Long websiteId;

    @Column("attachment_name")
    @ColumnComment("附件名称，包含文件后缀")
    @Schema(description = "附件名称", example = "政策文件.pdf")
    private String attachmentName;

    @Column("attachment_type")
    @ColumnComment("附件类型")
    private String attachmentType;

    @Column("attachment_size")
    @ColumnComment("附件大小，单位KB")
    private Integer attachmentSize;

    @Column("attachment_url")
    @ColumnComment("附件地址")
    @Schema(description = "附件地址", example = "https://www.example.com/files/policy.pdf")
    private String attachmentUrl;

    @Column("parent_article_id")
    @ColumnComment("附件所在的文章ID，先假设附件所在页面都应该是文章..?")
    private Long parentArticleId;

    // @Column(name = "pub_time", comment = "文章的发布时间", type = MySqlTypeConstant.DATETIME)
    // private Date pubTime;

    @Column(name = "create_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("创建时间")
    @DefaultValue("CURRENT_TIMESTAMP")
    private Date createTime;

    @Column(name = "update_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("更新时间")
    @IgnoreUpdate
    @DefaultValue("NULL ON UPDATE CURRENT_TIMESTAMP")
    private Date updateTime;

    @Column(name = "check_id")
    @ColumnComment("检测任务id")
    private Long checkId;

}
