package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsAutoIncrement;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsKey;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * init
 */

@Data
@TableName("t_login_log")
public class LoginLog {

    @TableId(type = IdType.AUTO)
    @IsKey
    @IsAutoIncrement
    @Column(name = "id", comment = "id")
    private Integer id;

    /**
     * 用户id
     */
    @Column(name = "user_id", comment = "用户id")
    private Integer userId;

    /**
     * 登录人
     */
    @Column(name = "user_name", comment = "登录人")
    private String userName;

    /**
     * 操作内容
     */
    @Column(name = "content", comment = "操作内容", type = MySqlTypeConstant.TEXT)
    private String content;

    /**
     * 登录时间
     */
    @Column(name = "login_time", comment = "登录时间", type = MySqlTypeConstant.DATETIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date loginTime;

    /**
     * ip地址
     */
    @Column(name = "ip", comment = "ip地址", type = MySqlTypeConstant.TEXT)
    private String ip;

    /**
     * 操作系统
     */
    @Column(name = "os_system", comment = "操作系统", type = MySqlTypeConstant.TEXT)
    private String osSystem;


}