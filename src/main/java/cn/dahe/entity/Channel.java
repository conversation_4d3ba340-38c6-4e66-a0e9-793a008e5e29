package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Entity;

import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import java.util.Date;

/**
 * 栏目配置
 */
@Entity
@Data
@TableName("t_channel")
public class Channel {

    @IsKey
    @IsAutoIncrement
    @Column("id")
    @ColumnComment("ID")
    private Integer id;

    /**
     * 栏目名称
     */
    @Column(value = "name")
    @ColumnComment("栏目名称")
    private String name;

    /**
     * 链接
     */
    @Column(value = "url")
    @ColumnComment("链接")
    private String url;

    /**
     * 抓取状态 0：禁用  1：开启
     */
    @Column(value = "enable", defaultValue = "1")
    @ColumnComment("抓取状态")
    private int enable;

    /**
     * 是否异步（动态js渲染）  0：非异步  1：异步
     */
    @Column(name = "async", comment = "是否异步（动态js渲染）  0：非异步  1：异步")
    @DefaultValue("1")
    private int async;

    /**
     * 网站站点id
     */
    @Column(name = "site_id", comment = "网站站点id，外键，website的id")
    private int siteId;

    /**
     * 网站名称/站点名称
     */
    @Column(value = "site_name", length = 500)
    @ColumnComment("网站名称/站点名称")
    private String siteName;


    /**
     * 解析处理类型
     * 0：网站  1：微博（接口调用）  2：微信  3：论坛  4：电子报  5：头条号  6：学习强国  7：百家号  8：网易号  9：一点资讯
     * 10：微博（程序抓取）  11：大河财立方  12：通用解析规则
     */
    @Column(name = "process_type", comment = "解析处理类型")
    private int processType;

    /**
     * 调度周期数值，默认60分钟
     */
    @Column(name = "scheduler_interval", comment = "调度周期数值")
    @DefaultValue("60")
    private int schedulerInterval;


    /**
     * 匹配文章链接规则，代表xpath区域，和下面的正则两个至少有一个
     */
    @Column(value = "xpath_url", length = 500)
    @ColumnComment("匹配文章链接规则")
    private String xpathUrl;

    /**
     * 匹配文章链接规则，代表正则，和上面的xpath两个至少有一个
     */
    @Column(value = "regex_url", length = 500)
    @ColumnComment("匹配文章链接规则")
    private String regexUrl;

    /**
     * 标题规则
     */
    @Column(value = "xpath_title", length = 500)
    @ColumnComment("标题规则")
    private String xpathTitle;

    /**
     * 正文规则
     */
    @Column(value = "xpath_content", length = 500)
    @ColumnComment("正文规则")
    private String xpathContent;

    /**
     * 来源规则
     */
    @Column(value = "xpath_source", length = 500)
    @ColumnComment("来源规则")
    private String xpathSource;

    /**
     * 发布时间规则
     */
    @Column(value = "xpath_pub_time", length = 500)
    @ColumnComment("发布时间规则")
    private String xpathPubTime;

    /**
     * 分页规则
     */
    @Column(value = "xpath_page", length = 500)
    @ColumnComment("分页规则")
    private String xpathPage;

    /**
     * xpath过滤规则
     */
    @Column(value = "filter_xpath", length = 500)
    @ColumnComment("xpath过滤规则")
    private String filterXpath;

    /**
     * 正则过滤规则
     */
    @Column(value = "filter_regex", length = 500)
    @ColumnComment("正则过滤规则")
    private String filterRegex;


    /**
     * 编辑人员配置规则
     */
    @Column(value = "xpath_editor", length = 500)
    @ColumnComment("正则过滤规则")
    private String xpathEditor;


    @Column(value = "create_user_id", defaultValue = "0")
    @ColumnComment("创建用户")
    private Integer createUserId;


    @Column(value = "create_user_name")
    @ColumnComment("创建用户")
    private String createUserName;


    @Column(name = "create_time", comment = "创建时间", type = MySqlTypeConstant.DATETIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    @Column(name = "last_modify_time", comment = "最后修改时间", type = MySqlTypeConstant.DATETIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastModifyTime;

    /**
     * 数据组栏目ID
     */
    @Column(name = "sjz_channel_id", comment = "数据组栏目id", defaultValue = "0")
    private Integer sjzChannelId;
}
