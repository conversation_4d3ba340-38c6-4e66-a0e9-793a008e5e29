package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 错误类型映射实体类
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
@TableName("t_check_error_type_mapping")
public class CheckErrorTypeMapping {

    @IsKey
    @IsAutoIncrement
    @TableId
    @Column("id")
    @ColumnComment("主键ID")
    private Long id;

    @Column("source_id")
    @ColumnComment("来源ID")
    private Long sourceId;

    @Column("source_error_code")
    @ColumnComment("来源错误编码")
    private String sourceErrorCode;

    @Column("source_error_name")
    @ColumnComment("来源错误名称")
    private String sourceErrorName;

    @Column("target_type_id")
    @ColumnComment("映射到的错误类型ID")
    private Long targetTypeId;

    @Column(name = "create_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("创建时间")
    private Date createTime;

    @Column(name = "update_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("更新时间")
    private Date updateTime;
} 