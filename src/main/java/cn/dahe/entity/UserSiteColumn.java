package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.ColumnComment;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 用户站点栏目关联表
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Data
@TableName("t_user_site_column")
@Schema(description = "用户站点栏目关联")
public class UserSiteColumn {

    @TableId(type = IdType.AUTO)
    @Schema(description = "编号")
    private Integer id;

    @Schema(description = "用户id")
    @Column("user_id")
    private Integer userId;

    @Schema(description = "用户姓名")
    @Column("user_name")
    private String userName;

    @Schema(description = "站点id")
    @Column("site_id")
    private Integer siteId;

    @Schema(description = "站点名称")
    @Column("site_name")
    private String siteName;

    @Schema(description = "站点地址")
    @Column("site_url")
    private String siteUrl;

    @Schema(description = "栏目id")
    @Column("column_id")
    private Integer columnId;

    @Schema(description = "栏目名称")
    @Column("column_name")
    private String columnName;

    @Schema(description = "栏目地址")
    @Column("column_url")
    private String columnUrl;

    @Schema(description = "创建时间")
    @Column("create_time")
    private Date createTime;

    @Schema(description = "创建人Id")
    @Column("create_by_user_id")
    private String createByUserId;

    @Schema(description = "创建人姓名")
    @Column("create_by_user_name")
    private String createByUserName;

    @Schema(description = "修改时间")
    @Column("update_time")
    private Date updateTime;

    @Schema(description = "修改人Id")
    @Column("update_by_user_id")
    private String updateByUserId;

    @Schema(description = "修改人姓名")
    @Column("update_by_user_name")
    private String updateByUserName;

}
