package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;

import java.util.Date;

/**
 * 站点转载信源关联表
 */
@Data
@TableName("t_website_reprint_source")
public class WebsiteReprintSource {
    
    /**
     * 主键ID
     */
    @IsKey
    @IsAutoIncrement
    @Column(value = "id",comment = "ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 站点ID
     */
    @Column(value = "website_id",comment = "站点ID")
    private Long websiteId;

    /**
     * 转载信源名称
     */
    @Column(value = "reprint_source",comment = "转载信源名称")
    private String reprintSource;

    /**
     * 是否过滤
     */
    @DefaultValue("0")
    @Column(value = "filter_status",comment = "是否过滤：0-不过滤，1-过滤",type = MySqlTypeConstant.TINYINT)
    private Boolean filterStatus;

    /**
     * 过滤时间
     */
    @Column(value = "filter_time",comment = "过滤时间",type = MySqlTypeConstant.DATETIME)
    private Date filterTime;

    /**
     * 创建时间
     */
    @DefaultValue("CURRENT_TIMESTAMP")
    @Column(value = "create_time",comment = "创建时间",type = MySqlTypeConstant.DATETIME)
    private Date createTime;

    /**
     * 更新时间
     */
    @IgnoreUpdate
    @DefaultValue("NULL ON UPDATE CURRENT_TIMESTAMP")
    @Column(value = "update_time",comment = "更新时间",type = MySqlTypeConstant.DATETIME)
    private Date updateTime;
}
