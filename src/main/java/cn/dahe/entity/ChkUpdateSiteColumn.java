package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 栏目更新检查实体类
 * 对应原型图功能需求
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@TableName("chk_update_site_column")
@Schema(name = "栏目更新检查", description = "网站栏目更新检查信息")
public class ChkUpdateSiteColumn {

    @IsKey
    @IsAutoIncrement
    @Column("id")
    @ColumnComment("编号")
    @Schema(description = "编号", example = "1")
    private Long id;

    @Column("group_name")
    @ColumnComment("分组")
    @Schema(description = "分组", example = "政府网站")
    private String groupName;

    @Column("column_name")
    @ColumnComment("栏目名称")
    @Schema(description = "栏目名称", example = "政务公开")
    private String columnName;

    @Column("column_category")
    @ColumnComment("栏目分类")
    @Schema(description = "栏目分类", example = "新闻类")
    private String columnCategory;

    @Column("column_id")
    @ColumnComment("栏目ID")
    @Schema(description = "栏目ID", example = "1")
    private Long columnId;

    @Column(name = "update_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("更新时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间", example = "2025-07-30 10:30:00")
    private Date updateTime;

    @Column("check_status")
    @ColumnComment("检测状态")
    @DefaultValue("0")
    @Schema(description = "检测状态(1:正常,2:异常,3:不可访问)", example = "1")
    private Integer checkStatus;

    @Column("website_url")
    @ColumnComment("网站链接")
    @Schema(description = "网站链接", example = "https://www.example.gov.cn")
    private String websiteUrl;

    @Column("snapshot_url")
    @ColumnComment("栏目快照地址")
    @Schema(description = "栏目快照地址", example = "https://snapshot.example.com/column/123456")
    private String snapshotUrl;

    @Column("snapshot_signature")
    @ColumnComment("栏目快照签名")
    @Schema(description = "栏目快照签名", example = "def456ghi789")
    private String snapshotSignature;

    @Column(name = "parse_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("解析时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "解析时间", example = "2025-07-30 10:30:00")
    private Date parseTime;

    @Column(name = "create_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("创建时间")
    @DefaultValue("CURRENT_TIMESTAMP")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间", example = "2025-07-30 10:30:00")
    private Date createTime;

    @Column("create_by")
    @ColumnComment("创建人")
    @Schema(description = "创建人", example = "1")
    private Integer createBy;

    @Column("update_by")
    @ColumnComment("更新人")
    @Schema(description = "更新人", example = "1")
    private Integer updateBy;

    @Column("is_del")
    @ColumnComment("是否删除")
    @DefaultValue("0")
    @Schema(description = "是否删除(0:否,1:是)", example = "0")
    private Integer isDel;
}
