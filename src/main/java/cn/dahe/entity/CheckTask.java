package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 文章检查内任务实体类
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@Accessors(chain = true)
@TableName("t_check_task")
public class CheckTask {

    @IsKey
    @IsAutoIncrement
    @TableId
    @Column(value = "id",comment = "主键ID")
    private Long id;

    @Column(name = "check_type", type = MySqlTypeConstant.TINYINT)
    @ColumnComment("检查类型 0 网站 1 网站附件 2 微博 3 微信公众号 4 新媒体其他平台")
    @DefaultValue("0")
    private Integer checkType;

    @Column(name = "check_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("检查时间")
    private Date checkTime;

    @Column(name = "check_status", comment = "检查状态 0未检查 1已检查 2检查失败待重试 3检查失败", type = MySqlTypeConstant.TINYINT)
    @DefaultValue("0")
    private Integer checkStatus;

    @Column(name = "create_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("创建时间")
    @DefaultValue("CURRENT_TIMESTAMP")
    private Date createTime;

    @Column(name = "update_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("更新时间")
    @IgnoreUpdate
    @DefaultValue("NULL ON UPDATE CURRENT_TIMESTAMP")
    private Date updateTime;

    /**
     * 检查任务失败类型：1-本地异常，2-接口超时，3-接口失败
     */
    @Column(name = "fail_type", comment = "检查任务失败类型：1-本地异常，2-接口超时，3-接口失败 0-未失败", type = MySqlTypeConstant.TINYINT)
    @DefaultValue("0")
    private Integer failType;

    /**
     * 检查任务失败原因（包含失败详情、响应时间等信息）
     */
    @Column(name = "fail_reason", type = MySqlTypeConstant.TEXT)
    @ColumnComment("检查任务失败原因")
    private String failReason;

    @Column(name = "check_strategy", comment = "检查策略 0 查全 1 查准", type = MySqlTypeConstant.TINYINT)
    @Schema(description = "检查策略 0 查全 1 查准", example = "0")
    @DefaultValue("0")
    private Integer checkStrategy;

    @Column(name = "content_length", comment = "正文字数")
    @DefaultValue("0")
    private Integer contentLength;
} 