package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;

import java.util.Date;

/**
 * 网站分组实体类
 */
@Data
@TableName("t_website_group")
public class WebsiteGroup {

    @IsKey
    @IsAutoIncrement
    @Column("id")
    @ColumnComment("ID")
    private Integer id;

    @Column("group_name")
    @ColumnComment("分组名称")
    private String groupName;

    @Column(name = "create_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("创建时间")
    private Date createTime;

    @Column(name = "update_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("更新时间")
    @IgnoreUpdate
    @DefaultValue("NULL ON UPDATE CURRENT_TIMESTAMP")
    private Date updateTime;


    @Column(name = "status", comment = "状态 0：停用 1：启用", type = MySqlTypeConstant.TINYINT)
    @DefaultValue("1")
    private Integer status;

    @TableLogic
    @Column(name = "is_del", comment = "是否删除 0：未删除 1：已删除", type = MySqlTypeConstant.TINYINT)
    @DefaultValue("0")
    private Boolean isDel;
} 