package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 采集文章实体类
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@TableName("t_article")
@Schema(name = "文章信息", description = "采集的文章基本信息")
public class Article {

    @IsKey
    @IsAutoIncrement
    @TableId
    @Column("id")
    @ColumnComment("主键ID")
    private Long id;

    @Column("website_id")
    @ColumnComment("关联的网站ID")
    private Long websiteId;

    @Column(value = "website_name")
    @ColumnComment("网站名称")
    private String websiteName;

    @Column("title")
    @ColumnComment("文章标题")
    private String title;

    @Column(name = "create_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("创建时间")
    @DefaultValue("CURRENT_TIMESTAMP")
    private Date createTime;


    @Column(name = "check_id")
    @ColumnComment("检测任务id")
    private Long checkId;

    @Column(name = "update_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("更新时间")
    @IgnoreUpdate
    @DefaultValue("NULL ON UPDATE CURRENT_TIMESTAMP")
    private Date updateTime;

    @Column("column_id")
    @ColumnComment("栏目ID")
    private Long columnId;

    /**
     * 文章的发布时间
     */
    @Column(name = "pub_time", comment = "文章的发布时间", type = MySqlTypeConstant.DATETIME)
    private Date pubTime;

    /**
     * 文章的成文时间
     */
    @Column(name = "write_time", comment = "文章的成文时间", type = MySqlTypeConstant.DATETIME)
    private Date writeTime;


    /**
     * 文章访问链接 TODO 如何针对http、https合并？
     */
    @Column(name = "article_url", type = MySqlTypeConstant.TEXT, comment = "访问链接")
    private String articleUrl;


    @Column(value = "snapshot", type = MySqlTypeConstant.TEXT, comment = "快照")
    @ColumnComment("快照")
    private String snapshot;


    /**
     * 作者
     */
    @Column(name = "author", type = MySqlTypeConstant.TEXT, comment = "作者")
    private String author;

    // @Column(name = "reprint_source", type = MySqlTypeConstant.TEXT)
    // @ColumnComment("来源，转载信源")
    // private String reprintSource;

    @Column(name = "reprint_source_id", type = MySqlTypeConstant.BIGINT)
    private Long reprintSourceId;

    /**
     * 审核状态
     */
    @Column(name = "audit_status", comment = "审核状态 0未审核 1审核通过 2审核驳回", type = MySqlTypeConstant.TINYINT)
    @DefaultValue("0")
    private Integer auditStatus;

    @Column(name = "audit_user_id")
    @ColumnComment("审核人ID")
    private Long auditUserId;

    @Column(name = "audit_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("审核时间")
    private Date auditTime;

    /**
     * 处置状态
     */
    @Column(name = "disposal_status", comment = "处置状态 0未处置 1已约谈整改 2已上报线索 3已关停站点", type = MySqlTypeConstant.TINYINT)
    @DefaultValue("0")
    private Integer disposalStatus;

    /**
     * 处置备注（最多200字）
     */
    @Column(name = "disposal_remark", comment = "处置备注", length = 200)
    @Schema(description = "处置备注 最多200字", example = "已与网站负责人沟通，要求立即整改")
    private String disposalRemark;

    /**
     * 整改状态（0未整改，1整改中，2已整改，3无需整改）
     */
    @Column(name = "rectify_status", comment = "整改状态 0未下发 1待整改 2已整改 3无需整改", type = MySqlTypeConstant.TINYINT)
    @DefaultValue("0")
    private Integer rectifyStatus;

}