package cn.dahe.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 网站访问记录VO
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Data
@Schema(description = "网站访问记录VO")
public class WebsiteAccessRecordVO {

    @Schema(description = "ID")
    private Integer id;

    @Schema(description = "网站ID")
    private Integer webId;

    @Schema(description = "网站名称")
    private String webName;

    @Schema(description = "网站地址")
    private String webUrl;

    @Schema(description = "访问耗时（毫秒）")
    private Double accessTimeConsuming;

    @Schema(description = "HTTP状态码")
    private Integer httpCode;

    @Schema(description = "是否访问成功：0否，1是")
    private Integer success;

    @Schema(description = "访问成功状态描述")
    public String getSuccessDesc() {
        return success != null && success == 1 ? "成功" : "失败";
    }

    @Schema(description = "链接类型：0内链，1外链")
    private Integer type;

    @Schema(description = "链接类型描述")
    public String getTypeDesc() {
        return type != null && type == 1 ? "外链" : "内链";
    }

    @Schema(description = "内容类型")
    private String contentType;

    @Schema(description = "链接地址")
    private String linkUrl;

    @Schema(description = "父链接地址")
    private String parentUrl;

    @Schema(description = "父链接发布时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date parentPubTime;

    @Schema(description = "检查时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;

    @Schema(description = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
