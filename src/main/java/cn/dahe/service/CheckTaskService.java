package cn.dahe.service;

import cn.dahe.model.dto.ArticleCheckExecuteDto;
import cn.dahe.model.dto.ContentCheckResultDto;
import cn.dahe.entity.Article;
import cn.dahe.entity.CheckTask;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 文章检查内容服务接口
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface CheckTaskService extends IService<CheckTask> {




    /**
     * 更新文章校对内容状态
     *
     * @param contentId   文章校对内容
     * @param checkResult 校对结果
     */
    void updateCheckContentStatus(Long contentId, ContentCheckResultDto checkResult);

    /**
     * 创建内容检查任务
     *
     * @param article 文章
     * @param title     文章标题
     * @param content   文章内容
     * @return 处理后的HTML结果
     */
    ArticleCheckExecuteDto initArticleCheckTask(Article article, String title, String content);

    /**
     * 处理校对结果并更新位置信息
     *
     * @param articleId 文章ID
     */
    void processCheckResults(Long articleId, ArticleCheckExecuteDto checkContentDto, ContentCheckResultDto checkResult);


} 