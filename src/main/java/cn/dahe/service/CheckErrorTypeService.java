package cn.dahe.service;

import cn.dahe.entity.CheckErrorType;
import cn.dahe.utils.SpringUtils;
import cn.dahe.model.vo.check.BaseErrorTypeVO;
import cn.dahe.model.vo.check.FirstLevelErrorTypeVO;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 错误类型Service
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
public interface CheckErrorTypeService extends BaseService<CheckErrorType> {

    /**
     * 获取错误类型树
     */
    List<FirstLevelErrorTypeVO> getTypeTree();

    /**
     * 获取指定层级的错误类型列表
     */
    List<CheckErrorType> getTypesByLevel(Integer level);

    /**
     * 获取指定父级的子类型列表
     */
    List<CheckErrorType> getChildTypes(Long parentId);

    List<BaseErrorTypeVO> listTypeName();

    /**
     * 刷新错误类型名称缓存
     */
    void refreshErrorTypeNameCache();

    Map<Long, String> TYPE_NAME_CACHE = new ConcurrentHashMap<>();

    /**
     * 错误类型层级关系缓存
     * key: 错误类型ID
     * value: [一级ID, 二级ID, 三级ID]，如果某级不存在则为null
     */
    Map<Long, Long[]> TYPE_HIERARCHY_CACHE = new ConcurrentHashMap<>();

    /**
     * 根据错误类型ID获取类型名称
     * 二级分类返回其分类名称
     * 三级分类返回"二级分类名-三级分类名"
     *
     * @param typeId 错误类型ID
     * @return String 错误类型名称
     */
    static String getTypeName(Long typeId) {
        if (TYPE_NAME_CACHE.isEmpty()) {
            SpringUtils.getBean(CheckErrorTypeService.class).refreshErrorTypeNameCache();
        }
        return TYPE_NAME_CACHE.get(typeId);
    }

    /**
     * 根据错误类型ID获取其层级ID数组
     * 返回数组包含[一级ID, 二级ID, 三级ID]
     * 如果是一级分类，则返回[一级ID, null, null]
     * 如果是二级分类，则返回[一级ID, 二级ID, null]
     * 如果是三级分类，则返回[一级ID, 二级ID, 三级ID]
     *
     * @param typeId 错误类型ID
     * @return Long[] 层级ID数组
     */
    static Long[] getTypeHierarchy(Long typeId) {
        if (TYPE_HIERARCHY_CACHE.isEmpty()) {
            SpringUtils.getBean(CheckErrorTypeService.class).refreshTypeHierarchyCache();
        }
        return TYPE_HIERARCHY_CACHE.get(typeId);
    }

    /**
     * 刷新错误类型层级缓存
     */
    void refreshTypeHierarchyCache();
} 