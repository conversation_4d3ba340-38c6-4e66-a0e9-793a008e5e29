package cn.dahe.service;

import cn.dahe.model.dto.PageResult;
import cn.dahe.entity.Attachment;
import cn.dahe.model.query.AttachmentCheckQuery;
import cn.dahe.model.vo.AttachmentCheckVO;

import java.io.IOException;

/**
 * 附件检查Service - 对应原型图功能
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface AttachmentService extends BaseService<Attachment> {

    // ==================== 附件检查记录 ====================

    /**
     * 分页查询附件检查记录
     *
     * @param query 查询参数
     * @return 分页结果
     */
    PageResult<AttachmentCheckVO> pageAttachmentAndCheckResults(AttachmentCheckQuery query);

    /**
     * 根据ID获取附件检查详情
     *
     * @param attachmentId ID
     * @return 详情
     */
    AttachmentCheckVO getAttachmentAndCheckResults(Long attachmentId,AttachmentCheckQuery query);

    // ==================== 数据导出 ====================

    /**
     * 导出附件检查记录
     *
     * @param query 查询参数
     */
    void export(AttachmentCheckQuery query) throws IOException;
}
