package cn.dahe.service.message;

import cn.dahe.common.redis.RedisStreamConfig;
import cn.dahe.model.message.ArticleMessage;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.stream.ObjectRecord;
import org.springframework.data.redis.connection.stream.StreamRecords;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;

/**
 * 文章消息生产者服务
 */
@Slf4j
@Service
public class ArticleMessageProducer {

    @Resource(name = "streamRedisTemplate")
    private RedisTemplate<String, Object> streamRedisTemplate;

    @PostConstruct
    public void init() {
        // 确保 Stream 存在
        Boolean hasKey = streamRedisTemplate.hasKey(RedisStreamConfig.ARTICLE_STREAM_KEY);
        if (Boolean.FALSE.equals(hasKey)) {
            try {
                // 创建消费组
                streamRedisTemplate.opsForStream().createGroup(RedisStreamConfig.ARTICLE_STREAM_KEY, RedisStreamConfig.ARTICLE_CONSUMER_GROUP);
            } catch (Exception e) {
                log.error("创建Stream消费组失败", e);
            }
        }
    }

    /**
     * 发送完整的文章消息到队列
     *
     * @param message 文章消息
     * @return 消息ID
     */
    public String sendMessage(ArticleMessage message) {
        try {
            String messageJson = JSON.toJSONString(message);
            ObjectRecord<String, String> record = StreamRecords.newRecord()
                    .ofObject(messageJson)
                    .withStreamKey(RedisStreamConfig.ARTICLE_STREAM_KEY);

            // 发送消息到 Stream
            String messageId = streamRedisTemplate.opsForStream().add(record).getValue();
            log.info("发送文章消息成功: messageId={}, articleId={}", messageId, message.getArticleId());
            return messageId;
        } catch (Exception e) {
            log.error("发送文章消息失败: {}", message, e);
            throw new RuntimeException("发送文章消息失败", e);
        }
    }

    /**
     * 批量发送文章消息到队列
     *
     * @param messages 文章消息列表
     */
    public void sendMessages(List<ArticleMessage> messages) {
        for (ArticleMessage message : messages) {
            try {
                sendMessage(message);
            } catch (Exception e) {
                log.error("发送文章消息失败: {}", message, e);
            }
        }
    }
}