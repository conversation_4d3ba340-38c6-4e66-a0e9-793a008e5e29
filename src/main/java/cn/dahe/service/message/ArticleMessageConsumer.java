package cn.dahe.service.message;

import cn.dahe.common.redis.RedisStreamConfig;
import cn.dahe.model.message.ArticleMessage;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.stream.*;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.stream.StreamMessageListenerContainer;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.UUID;

/**
 * 文章消息消费者服务
 */
@Slf4j
@Service
public class ArticleMessageConsumer {

    @Resource(name = "streamRedisTemplate")
    private RedisTemplate<String, Object> streamRedisTemplate;
    private final StreamMessageListenerContainer<String, ObjectRecord<String, String>> streamMessageListenerContainer;
    private final String consumerId;
    private final int MAX_RETRY_COUNT = 3;

    public ArticleMessageConsumer(StreamMessageListenerContainer<String, ObjectRecord<String, String>> streamMessageListenerContainer) {
        this.streamMessageListenerContainer = streamMessageListenerContainer;
        this.consumerId = RedisStreamConfig.CONSUMER_PREFIX + UUID.randomUUID();
    }

    @PostConstruct
    public void init() {
        // 确保消费组存在
        try {
            streamRedisTemplate.opsForStream().createGroup(RedisStreamConfig.ARTICLE_STREAM_KEY, RedisStreamConfig.ARTICLE_CONSUMER_GROUP);
        } catch (Exception e) {
            log.warn("消费组已存在: {}", RedisStreamConfig.ARTICLE_CONSUMER_GROUP);
        }

        // 启动消费者监听
        startConsumer();
    }

    @PreDestroy
    public void destroy() {
        if (streamMessageListenerContainer != null) {
            streamMessageListenerContainer.stop();
        }
    }

    private void startConsumer() {
        // 创建消费者
        Consumer consumer = Consumer.from(RedisStreamConfig.ARTICLE_CONSUMER_GROUP, consumerId);

        // 从最后一个未确认的消息开始消费
        StreamOffset<String> streamOffset = StreamOffset.create(RedisStreamConfig.ARTICLE_STREAM_KEY, ReadOffset.lastConsumed());

        // 配置消费者选项
        StreamMessageListenerContainer.StreamReadRequest<String> streamReadRequest = StreamMessageListenerContainer
                .StreamReadRequest
                .builder(streamOffset)
                .consumer(consumer)
                .autoAcknowledge(false)
                .cancelOnError(throwable -> false)
                .errorHandler(throwable -> log.error("处理消息异常", throwable))
                .build();

        // 注册消息监听器
        streamMessageListenerContainer.register(streamReadRequest, this::handleMessage);

        // 启动消费者容器
        streamMessageListenerContainer.start();
        log.info("文章消息消费者启动成功: consumerId={}", consumerId);
    }

    /**
     * 处理消息
     */
    private void handleMessage(ObjectRecord<String, String> message) {
        String messageId = message.getId().getValue();
        String messageJson = message.getValue();

        try {
            // 解析消息
            ArticleMessage articleMessage = JSON.parseObject(messageJson, ArticleMessage.class);
            articleMessage.setMessageId(messageId);

            // 处理消息
            processArticleMessage(articleMessage);

            // 确认消息
            streamRedisTemplate.opsForStream().acknowledge(RedisStreamConfig.ARTICLE_STREAM_KEY,
                    RedisStreamConfig.ARTICLE_CONSUMER_GROUP, messageId);

            log.info("消息处理成功: messageId={}", messageId);
        } catch (Exception e) {
            log.error("消息处理失败: messageId={}, message={}", messageId, messageJson, e);
            handleMessageError(messageJson, messageId, e);
        }
    }

    /**
     * 处理文章消息
     * 由于已经包含了完整的文章信息，直接进行校对处理
     */
    private void processArticleMessage(ArticleMessage message) {
        // TODO: 实现文章校对逻辑
        // 1. 创建校对任务
        // 2. 处理文章内容
        // 3. 更新任务状态
        log.info("开始处理文章: articleId={}, title={}", message.getArticleId(), message.getTitle());
    }

    /**
     * 处理消息错误
     */
    private void handleMessageError(String messageJson, String messageId, Exception e) {
        try {
            ArticleMessage message = JSON.parseObject(messageJson, ArticleMessage.class);
            int retryCount = message.getRetryCount() != null ? message.getRetryCount() : 0;

            if (retryCount < MAX_RETRY_COUNT) {
                // 增加重试次数
                message.setRetryCount(retryCount + 1);

                // 重新发送消息
                String newMessageJson = JSON.toJSONString(message);
                ObjectRecord<String, String> record = StreamRecords.newRecord()
                        .ofObject(newMessageJson)
                        .withStreamKey(RedisStreamConfig.ARTICLE_STREAM_KEY);

                streamRedisTemplate.opsForStream().add(record);

                // 确认原消息
                streamRedisTemplate.opsForStream().acknowledge(RedisStreamConfig.ARTICLE_STREAM_KEY,
                        RedisStreamConfig.ARTICLE_CONSUMER_GROUP, messageId);

                log.info("消息重试: messageId={}, retryCount={}", messageId, retryCount + 1);
            } else {
                // 超过最大重试次数，确认消息并记录错误
                streamRedisTemplate.opsForStream().acknowledge(RedisStreamConfig.ARTICLE_STREAM_KEY,
                        RedisStreamConfig.ARTICLE_CONSUMER_GROUP, messageId);
                log.error("消息处理失败，超过最大重试次数: messageId={}, retryCount={}", messageId, retryCount);

                // TODO: 可以将失败的消息存储到死信队列或错误日志中
            }
        } catch (Exception ex) {
            log.error("处理消息错误失败: messageId={}", messageId, ex);
        }
    }
}