package cn.dahe.service;

import cn.dahe.model.dto.ArticleCheckDto;
import cn.dahe.entity.CheckWord;
import java.util.List;
import java.util.Map;

/**
 * 文章检查错误词库Service接口
 */
public interface CheckWordService extends BaseService<CheckWord> {

    /**
     * 获取或创建错误记录
     *
     * @return 错误记录ID
     */
    Long getOrCreateCheckWord(ArticleCheckDto checkDto);

    /**
     * 更新过滤状态
     */
    boolean updateFilterStatus(Long wordId, Boolean filterStatus);

    /**
     * 批量获取或创建错误记录
     *
     * @param checkDtos 错误记录DTO列表
     * @return 错误词到错误词实体的映射
     */
    Map<String, CheckWord> batchGetOrCreateCheckWords(List<ArticleCheckDto> checkDtos);
} 