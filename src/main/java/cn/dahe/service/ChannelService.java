package cn.dahe.service;

import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.entity.Channel;
import cn.dahe.model.query.ChannelQuery;
import cn.dahe.model.vo.LoginUserVO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <AUTHOR>
 * @date 2023-08-11
 */
public interface ChannelService extends IService<Channel> {

    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    PageResult<Channel> page(ChannelQuery query);

    /**
     * 添加
     *
     * @param Channel
     * @param user
     * @return
     */
    Result<String> save(Channel Channel, LoginUserVO user);

    /**
     * 添加
     *
     * @param Channel
     * @param user
     * @return
     */
    Result<String> update(Channel Channel, LoginUserVO user);


    /**
     * 禁用or启用
     *
     * @param id
     * @param user
     * @return
     */
    Result<String> updateStatus(String id, LoginUserVO user);


}
