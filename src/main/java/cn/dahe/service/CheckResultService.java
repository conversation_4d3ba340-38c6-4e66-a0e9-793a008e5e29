package cn.dahe.service;

import cn.dahe.entity.CheckContent;
import cn.dahe.entity.CheckResult;
import cn.dahe.entity.CheckTask;
import cn.dahe.enums.AuditStatusEnum;
import cn.dahe.model.dto.ContentCheckResultDto;
import cn.dahe.model.query.CheckResultQuery;
import cn.dahe.model.vo.CheckVO;

import java.util.List;

/**
 * 文章错误检查Service
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface CheckResultService extends BaseService<CheckResult> {


    /**
     * 执行文章内容校对
     *
     * @return 校对结果DTO，包含校对状态和结果
     */
    ContentCheckResultDto executeContentCheck(CheckTask checkTask, CheckContent checkContent);


    /**
     * 更新审核状态
     *
     * @param resultIds   检查ID列表
     * @param auditStatus 审核状态
     * @return 处理结果
     */
    Boolean updateAuditStatus(Long articleId, List<Long> resultIds, AuditStatusEnum auditStatus);


    <T extends CheckVO, U extends CheckResultQuery> void markCheckContentWithHtml(T vo, U query);

} 