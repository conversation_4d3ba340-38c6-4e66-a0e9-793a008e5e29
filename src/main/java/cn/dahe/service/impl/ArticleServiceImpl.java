package cn.dahe.service.impl;

import cn.dahe.common.auth.SecurityUtils;
import cn.dahe.dao.ArticleDao;
import cn.dahe.model.dto.ArticleSaveDto;
import cn.dahe.model.dto.Result;
import cn.dahe.entity.Article;
import cn.dahe.entity.CheckTask;
import cn.dahe.enums.ArticleDisposalStatusEnum;
import cn.dahe.enums.AuditStatusEnum;
import cn.dahe.model.query.ArticleCheckQuery;
import cn.dahe.service.CheckResultService;
import cn.dahe.service.ArticleService;
import cn.dahe.utils.ListUtils;
import cn.dahe.utils.StringUtils;
import cn.dahe.model.vo.ArticleCheckVO;
import cn.dahe.model.vo.LoginUserVO;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 采集文章Service实现类
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Service
public class ArticleServiceImpl extends BaseServiceImpl<ArticleDao, Article> implements ArticleService {

    @Resource
    private CheckResultService checkResultService;

    @Override
    public ArticleCheckVO getArticleAndCheckResults(Long articleId, ArticleCheckQuery query) {
        ArticleCheckVO result = this.getBaseMapper().getArticleInfo(articleId, query);
        checkResultService.markCheckContentWithHtml(result, query);
        return result;
    }

    @Override
    public IPage<ArticleCheckVO> pageArticleAndCheckResults(ArticleCheckQuery query) {
        //  TODO 限制分页查询的单页最大长度
        //  TODO 先行检查站点合法性
        IPage<ArticleCheckVO> result = this.getBaseMapper().pageArticleInfo(Page.of(query.getPage(), query.getLimit()), query);
        result.getRecords().forEach(vo -> checkResultService.markCheckContentWithHtml(vo, query));
        return result;
    }

    @Override
    public boolean updateAuditStatus(List<Long> articleIds, AuditStatusEnum auditStatus) {
        //  TODO 日志
        LoginUserVO loginUser = SecurityUtils.getLoginUser();
        LambdaUpdateChainWrapper<Article> updateWrapper = this.lambdaUpdate();
        updateWrapper.set(Article::getAuditStatus, auditStatus.getType());
        if (auditStatus != AuditStatusEnum.WAITING_FOR_REVIEW) {
            updateWrapper.set(Article::getAuditUserId, loginUser.getUserId());
            updateWrapper.set(Article::getAuditTime, new Date());
        }
        updateWrapper.in(Article::getId, articleIds);
        return updateWrapper.update();
    }

    @Override
    public Result<String> updateDisposalStatus(String articleIds,
                                               int disposalStatus,
                                               String remark,
                                               LoginUserVO loginUserVO) {
        if (StringUtils.isBlank(articleIds)) {
            return Result.error("文章ID不能为空");
        }
        ArticleDisposalStatusEnum enumByType = ArticleDisposalStatusEnum.getEnumByType(disposalStatus);
        if (enumByType == null) {
            return Result.error("处置状态选择错误");
        }
        boolean b = updateupdateDisposalStatusByIdsAndStatus(articleIds, enumByType.getType(), remark);
        if (!b) {
            return Result.error();
        }
        return Result.ok();
    }

    private boolean updateupdateDisposalStatusByIdsAndStatus(String articleIds, int status, String remark) {
        UpdateWrapper<Article> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("disposal_status", status);
        updateWrapper.set("disposal_remark", remark);
        updateWrapper.in("id", ListUtils.transferIdsToList(articleIds));
        return this.update(updateWrapper);
    }


    @Override
    public Article savePushArticle(ArticleSaveDto saveDTO) {
        Article article = new Article();
        article.setWebsiteId(saveDTO.getWebsiteId());
        article.setArticleUrl(saveDTO.getUrl());
        article.setTitle(saveDTO.getTitle());

        article.setCreateTime(new Date());
        // article.setAcquisitionTime(new Date());
        article.setPubTime(saveDTO.getPubTime());
        article.setWriteTime(saveDTO.getWriteTime());
        // 设置可选字段 TODO
        // article.setReprintSource(saveDTO.getSource());
        article.setAuthor(saveDTO.getAuthor());
        article.setColumnId(saveDTO.getChannelId());

        this.save(article);
        return article;
    }

    @Override
    public void updateCheckInfo(Long articleId, Long articleContentId) {
        this.lambdaUpdate()
                .eq(Article::getId, articleId)
                .set(Article::getCheckId, articleContentId)
                .update();
    }

    @Override
    public void updateArticleCheckRelation(Long articleId, CheckTask checkTask) {
        Long articleContentId = checkTask.getId();
        this.lambdaUpdate().eq(Article::getId, articleId)
                .set(Article::getCheckId, articleContentId)
                .update();
    }


}