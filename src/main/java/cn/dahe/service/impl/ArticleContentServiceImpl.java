package cn.dahe.service.impl;

import cn.dahe.dao.ArticleContentDao;
import cn.dahe.entity.ArticleContent;
import cn.dahe.service.ArticleContentService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 文章原文服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Service
public class ArticleContentServiceImpl extends ServiceImpl<ArticleContentDao, ArticleContent> implements ArticleContentService {
    
    @Override
    public ArticleContent getByArticleId(Long articleId) {
        return getById(articleId);
    }
} 