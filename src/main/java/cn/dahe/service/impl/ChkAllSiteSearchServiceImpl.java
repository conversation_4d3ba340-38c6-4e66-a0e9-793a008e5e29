package cn.dahe.service.impl;

import cn.dahe.dao.ChkAllSiteSearchDao;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.entity.ChkAllSiteSearch;
import cn.dahe.model.query.ChkAllSiteSearchQuery;
import cn.dahe.service.ChkAllSiteSearchService;
import cn.dahe.model.vo.ChkAllSiteSearchVO;
import cn.dahe.model.vo.LoginUserVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 全站搜索Service实现类
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
@Service
public class ChkAllSiteSearchServiceImpl extends BaseServiceImpl<ChkAllSiteSearchDao, ChkAllSiteSearch> implements ChkAllSiteSearchService {

    @Override
    public PageResult<ChkAllSiteSearchVO> page(ChkAllSiteSearchQuery query) {
        try {
            Page<ChkAllSiteSearchVO> page = new Page<>(query.getPage(), query.getLimit());

            IPage<ChkAllSiteSearchVO> result = baseMapper.selectPageWithExtInfo(page, query);

            // 如果没有数据，返回兜底数据
            if (result.getRecords().isEmpty()) {
                return createMockPageData(query);
            }

            return PageResult.page(result);
        } catch (Exception e) {
            log.error("查询全站搜索记录失败，返回兜底数据", e);
            return createMockPageData(query);
        }
    }

    @Override
    public Result<String> pullDataFromCollectionCenter(LoginUserVO user) {
        try {
            log.info("用户{}触发从采集中心拉取全站搜索数据", user.getUserId());

            // TODO: 实现从采集中心拉取数据的逻辑
            // 1. 调用采集中心API获取搜索数据
            // 2. 解析数据并转换为ChkAllSiteSearch实体
            // 3. 批量插入或更新数据库

            log.info("从采集中心拉取全站搜索数据成功");
            return Result.ok("拉取数据成功，共处理0条记录");
        } catch (Exception e) {
            log.error("从采集中心拉取全站搜索数据失败", e);
            return Result.error("拉取数据失败：" + e.getMessage());
        }
    }

    /**
     * 创建分页数据兜底数据
     */
    private PageResult<ChkAllSiteSearchVO> createMockPageData(ChkAllSiteSearchQuery query) {
        List<ChkAllSiteSearchVO> mockList = new ArrayList<>();

        String[] websites = {"河南师范大学", "中原工学院", "河南理工大学"};
        String[] groups = {"高校网站", "政府网站", "企业网站"};
        String[] titles = {
            "关于开展教学质量评估的通知",
            "2025年招生简章发布",
            "学术会议征文通知"
        };

        int limit = query.getLimit() != null ? query.getLimit() : 10;
        for (int i = 0; i < Math.min(limit, 3); i++) {
            ChkAllSiteSearchVO vo = new ChkAllSiteSearchVO();
            vo.setId((long) (i + 1));
            vo.setWebsiteName(websites[i]);
            vo.setGroupName(groups[i]);
            vo.setTitle(titles[i]);
            vo.setContentSummary("这是第" + (i + 1) + "条搜索结果的内容摘要...");
            vo.setUrl("https://www.example" + (i + 1) + ".edu.cn/news/" + (i + 1));
            vo.setPublishTime(new Date(System.currentTimeMillis() - (i + 1) * 24 * 60 * 60 * 1000L));
            vo.setContentTypeName("文章");
            vo.setFilterStatusName("未过滤");
            vo.setMatchRate((85 + i * 5) + "%");
            vo.setCreateTime(new Date());
            mockList.add(vo);
        }

        return new PageResult<>(mockList, 3L, limit, query.getPage() != null ? query.getPage() : 1);
    }
}
