package cn.dahe.service.impl;

import cn.dahe.dao.WebsiteDeadLinkCheckRecordDao;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.WebsiteAccessOverviewDto;
import cn.dahe.model.dto.WebsiteDeadLinkCheckRecordDto;
import cn.dahe.model.dto.WebsiteUpdateStatsDto;
import cn.dahe.entity.Website;
import cn.dahe.entity.WebsiteDeadLinkCheckRecord;
import cn.dahe.model.query.WebsiteDeadLinkCheckRecordQuery;
import cn.dahe.service.WebsiteDeadLinkCheckRecordService;
import cn.dahe.service.WebsiteService;
import cn.dahe.utils.ListUtils;
import cn.dahe.utils.NumberUtils;
import cn.dahe.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 *
 */
@Service
@AllArgsConstructor
public class WebsiteDeadLinkCheckRecordServiceImpl extends BaseServiceImpl<WebsiteDeadLinkCheckRecordDao, WebsiteDeadLinkCheckRecord> implements WebsiteDeadLinkCheckRecordService {

    @Resource
    private WebsiteService websiteService;


    @Override
    public PageResult<WebsiteDeadLinkCheckRecord> page(WebsiteDeadLinkCheckRecordQuery query) {
        IPage<WebsiteDeadLinkCheckRecord> dataPage = baseMapper.selectPage(new Page(query.getPage(), query.getLimit()), getWrapper(query));
        return new PageResult<>(dataPage.getRecords(), dataPage);
    }

    @Override
    public PageResult<WebsiteDeadLinkCheckRecordDto> pageDeadLinkDetail(WebsiteDeadLinkCheckRecordQuery query) {
        IPage<WebsiteDeadLinkCheckRecord> dataPage = baseMapper.selectPage(new Page(query.getPage(), query.getLimit()), getWrapper(query));
        return new PageResult<>(converToDto(dataPage.getRecords(), query), dataPage);
    }

    @Override
    public PageResult<WebsiteDeadLinkCheckRecord> pageCheckRecordDetail(WebsiteDeadLinkCheckRecordQuery query) {
        IPage<WebsiteDeadLinkCheckRecord> dataPage = baseMapper.selectPage(new Page(query.getPage(), query.getLimit()), getWrapper(query));
        return new PageResult<>(dataPage.getRecords(), dataPage);
    }


    @Override
    public WebsiteUpdateStatsDto totalOverview(WebsiteDeadLinkCheckRecordQuery query) {
        List<Website> websiteList = websiteService.listByIds(ListUtils.transferIdsToList(query.getWebId()));
        List<WebsiteAccessOverviewDto> overviewDtoList = new ArrayList<>();

        for (Website website : websiteList) {
            WebsiteAccessOverviewDto dto = buildWebsiteAccessOverview(website, query);
            overviewDtoList.add(dto);
        }

        int totalSites = overviewDtoList.size();


        // 按 errorCount 是否为0 分区：正常站点 & 出错站点
        Map<Boolean, List<WebsiteAccessOverviewDto>> partitioned = overviewDtoList.stream()
                .collect(Collectors.partitioningBy(dto -> dto.getErrorCount() == 0));

        int normalSites = partitioned.getOrDefault(true, Collections.emptyList()).size();
        int errorSites = totalSites - normalSites;

        String errorRate = NumberUtils.calculateRatioPercentage(errorSites, totalSites, 2);

        WebsiteUpdateStatsDto statsDto = new WebsiteUpdateStatsDto();
        statsDto.setTotalSites(totalSites);
        statsDto.setNormalSites(normalSites);
        statsDto.setErrorSites(errorSites);
        statsDto.setErrorRate(errorRate);

        return statsDto;
    }

    @Override
    public PageResult<WebsiteAccessOverviewDto> pageWebsiteStats(WebsiteDeadLinkCheckRecordQuery query) {
        PageResult<Website> websitePageResult = websiteService.pageByWebIds(query.getWebId(), query.getPage(), query.getLimit());
        List<WebsiteAccessOverviewDto> overviewDtoList = new ArrayList<>();

        for (Website website : websitePageResult.getList()) {
            WebsiteAccessOverviewDto dto = buildWebsiteAccessOverview(website, query);
            overviewDtoList.add(dto);
        }

        return new PageResult<>(overviewDtoList, websitePageResult.getTotal(), websitePageResult.getPageSize(), websitePageResult.getPage());
    }


    private WebsiteAccessOverviewDto buildWebsiteAccessOverview(Website website, WebsiteDeadLinkCheckRecordQuery query) {
        WebsiteAccessOverviewDto dto = new WebsiteAccessOverviewDto();
        dto.setId(website.getId());
        dto.setWebUrl(website.getWebUrl());
        dto.setWebName(website.getWebName());

        int totalCount = baseMapper.countByFilters(
                String.valueOf(website.getId()),
                query.getSourcePage(),
                query.getLinkUrl(),
                query.getHttpCode(),
                query.getBeginTime(),
                query.getEndTime()
        );

        int errorCount = baseMapper.countGroupRowsByWebIdAndDeadLinkUrl(
                String.valueOf(website.getId()),
                query.getSourcePage(),
                query.getLinkUrl(),
                query.getHttpCode(),
                query.getBeginTime(),
                query.getEndTime()
        );

        String errorRate = NumberUtils.calculateRatioPercentage(errorCount, totalCount, 2);

        dto.setTotalCount(totalCount);
        dto.setErrorCount(errorCount);
        dto.setErrorRate(errorRate);

        return dto;
    }


    private List<WebsiteDeadLinkCheckRecordDto> converToDto(List<WebsiteDeadLinkCheckRecord> list, WebsiteDeadLinkCheckRecordQuery query) {
        ArrayList<WebsiteDeadLinkCheckRecordDto> dtoArrayList = new ArrayList<>();
        for (WebsiteDeadLinkCheckRecord websiteDeadLinkCheckRecord : list) {
            WebsiteDeadLinkCheckRecordDto dto = new WebsiteDeadLinkCheckRecordDto();
            BeanUtils.copyProperties(websiteDeadLinkCheckRecord, dto);
            int countNumber = baseMapper.countGroupedByWebIdAndSourcePageAndDeadLinkUrl(String.valueOf(websiteDeadLinkCheckRecord.getWebId()),
                    websiteDeadLinkCheckRecord.getSourcePage(),
                    websiteDeadLinkCheckRecord.getLinkUrl(),
                    query.getHttpCode(),
                    query.getBeginTime(), query.getEndTime());
            dto.setCheckCount(countNumber);
            dtoArrayList.add(dto);
        }
        return dtoArrayList;
    }


    private QueryWrapper<WebsiteDeadLinkCheckRecord> getWrapper(WebsiteDeadLinkCheckRecordQuery query) {
        QueryWrapper<WebsiteDeadLinkCheckRecord> queryWrapper = Wrappers.query();
        if (StringUtils.isNotBlank(query.getWebId())) {
            queryWrapper.in("web_id", ListUtils.transferIdsToList(query.getWebId()));
        }
        if (StringUtils.isNotBlank(query.getBeginTime())) {
            queryWrapper.ge("check_time", query.getBeginTime());
        }
        if (StringUtils.isNotBlank(query.getEndTime())) {
            queryWrapper.le("check_time", query.getEndTime());
        }

        // 死链-模糊搜索
        if (StringUtils.isNotBlank(query.getLinkUrlLike())) {
            queryWrapper.like("link_url", query.getLinkUrlLike());
        }
        // 死链-准确搜索
        if (StringUtils.isNotBlank(query.getLinkUrl())) {
            queryWrapper.eq("link_url", query.getLinkUrl());
        }
        // 来源页面
        if (StringUtils.isNotBlank(query.getSourcePage())) {
            queryWrapper.eq("source_page", query.getSourcePage());
        }
        if (StringUtils.isNotBlank(query.getLinkType())) {
            queryWrapper.eq("type", query.getLinkType());
        }
        if (StringUtils.isNotBlank(query.getLatest())) {
            queryWrapper.eq("latest", query.getLatest());
        }
        queryWrapper.orderByDesc("check_time");
        return queryWrapper;
    }


}