package cn.dahe.service.impl;

import cn.dahe.dao.WebsiteReprintSourceDao;
import cn.dahe.entity.WebsiteReprintSource;
import cn.dahe.model.query.WebsiteReprintSourceQuery;
import cn.dahe.service.WebsiteReprintSourceService;
import cn.dahe.model.vo.WebsiteReprintSourceVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 站点转载信源关联表 服务实现类
 */
@Service
public class WebsiteReprintSourceServiceImpl extends ServiceImpl<WebsiteReprintSourceDao, WebsiteReprintSource> implements WebsiteReprintSourceService {

    @Override
    public Page<WebsiteReprintSourceVO> pageList(WebsiteReprintSourceQuery query) {
        return this.getBaseMapper().pageList(Page.of(query.getPage(), query.getLimit()), query);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAddOrUpdateFilter(List<Long> websiteIds, List<String> reprintSources) {
        if (CollectionUtils.isEmpty(websiteIds)) {
            return;
        }

        // 过滤空的转载信源并去重
        reprintSources = reprintSources.stream()
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        
        if (CollectionUtils.isEmpty(reprintSources)) {
            return;
        }

        // 查询已存在的记录
        LambdaQueryWrapper<WebsiteReprintSource> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(WebsiteReprintSource::getWebsiteId, websiteIds)
                .in(WebsiteReprintSource::getReprintSource, reprintSources);
        List<WebsiteReprintSource> existRecords = baseMapper.selectList(wrapper);

        // 构建已存在的websiteId和reprintSource组合的Set，用于快速查找
        Set<String> existPairs = existRecords.stream()
                .map(record -> record.getWebsiteId() + ":" + record.getReprintSource())
                .collect(Collectors.toSet());

        // 构建需要新增的记录
        List<WebsiteReprintSource> batchList = new ArrayList<>();
        Date now = new Date();

        for (Long websiteId : websiteIds) {
            for (String reprintSource : reprintSources) {
                String pair = websiteId + ":" + reprintSource;
                if (!existPairs.contains(pair)) {
                    WebsiteReprintSource newRecord = new WebsiteReprintSource();
                    newRecord.setWebsiteId(websiteId);
                    newRecord.setReprintSource(reprintSource);
                    newRecord.setFilterStatus(true); // 默认设置为过滤状态
                    newRecord.setFilterTime(now);
                    batchList.add(newRecord);
                }
            }
        }

        // 批量插入新记录
        if (CollectionUtils.isNotEmpty(batchList)) {
            saveBatch(batchList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateFilterStatus(List<Long> ids, Boolean filterStatus) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        // 构建更新条件
        LambdaQueryWrapper<WebsiteReprintSource> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(WebsiteReprintSource::getId, ids);

        // 构建更新内容
        WebsiteReprintSource updateEntity = new WebsiteReprintSource();
        updateEntity.setFilterStatus(filterStatus);
        updateEntity.setFilterTime(filterStatus ? new Date() : null); // 如果是设置为过滤，则更新过滤时间

        // 执行批量更新
        update(updateEntity, wrapper);
    }
}
