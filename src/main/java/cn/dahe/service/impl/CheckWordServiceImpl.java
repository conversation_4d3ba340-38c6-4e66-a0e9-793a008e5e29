package cn.dahe.service.impl;

import cn.dahe.dao.CheckWordDao;
import cn.dahe.model.dto.ArticleCheckDto;
import cn.dahe.entity.CheckWord;
import cn.dahe.service.CheckWordService;
import cn.dahe.service.CheckErrorTypeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 文章检查错误词库Service实现类
 */
@Service
public class CheckWordServiceImpl extends ServiceImpl<CheckWordDao, CheckWord> implements CheckWordService {



    @Override
    public boolean updateFilterStatus(Long wordId, Boolean filterStatus) {
        return this.lambdaUpdate()
                .eq(CheckWord::getId, wordId)
                .set(CheckWord::getFilterStatus, filterStatus)
                .set(CheckWord::getUpdateTime, new Date())
                .update();
    }

    /**
     * TODO 缓存
     *
     * @param checkDto
     */
    @Override
    public Long getOrCreateCheckWord(ArticleCheckDto checkDto) {
        String errorWord = checkDto.getErrorWord();
        String suggestWord = checkDto.getSuggestWord();
        Long errorType = checkDto.getErrorType();
        Integer errorLevel = checkDto.getErrorLevel();
        // 1. 先查找是否已存在相同的错误词和建议词
        CheckWord word = this.getBaseMapper().selectDistinctWord(errorWord);
        if (word == null) {
            // 2. 如果不存在，创建新记录
            word = new CheckWord();
            word.setErrorWord(errorWord);
            word.setSuggestWord(suggestWord);
            Long[] typeHierarchy = CheckErrorTypeService.getTypeHierarchy(errorType);
            word.setFirstErrorType(typeHierarchy[0]);
            word.setSecondErrorType(typeHierarchy[1]);
            word.setThirdErrorType(typeHierarchy[2]);
            word.setErrorLevel(errorLevel);
            word.setFilterStatus(false);
            word.setCreateTime(new Date());
            word.setUpdateTime(new Date());
            this.save(word);
        }
        return word.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, CheckWord> batchGetOrCreateCheckWords(List<ArticleCheckDto> checkDtos) {
        if (CollectionUtils.isEmpty(checkDtos)) {
            return new HashMap<>();
        }

        // 1. 收集所有错误词
        Set<String> allErrorWords = checkDtos.stream()
                .map(ArticleCheckDto::getErrorWord)
                .collect(Collectors.toSet());

        // 2. 批量查询已存在的错误词
                 Map<String, CheckWord> resultMap = this.getBaseMapper().selectBatchDistinctWord(new ArrayList<>(allErrorWords)).stream()
                 .collect(Collectors.toMap(CheckWord::getErrorWord, Function.identity()));

         // 筛选需要新增的词（不在已有结果中）
        List<CheckWord> newWords = checkDtos.stream()
                // 只处理不在已有结果中的词，且每个词只处理一次
                .filter(dto -> !resultMap.containsKey(dto.getErrorWord()))
                .collect(Collectors.toMap(
                        ArticleCheckDto::getErrorWord,
                        Function.identity(),
                        (existing, replacement) -> existing // 重复时保留第一个
                ))
                .values().stream()
                // 转换为实体对象
                .map(dto -> {
                    CheckWord word = new CheckWord();
                    word.setErrorWord(dto.getErrorWord());
                    word.setSuggestWord(dto.getSuggestWord());
                    Long[] typeHierarchy = CheckErrorTypeService.getTypeHierarchy(dto.getErrorType());
                    word.setFirstErrorType(typeHierarchy[0]);
                    word.setSecondErrorType(typeHierarchy[1]);
                    word.setThirdErrorType(typeHierarchy[2]);
                    word.setErrorLevel(dto.getErrorLevel());
                    word.setFilterStatus(false);
                    word.setCreateTime(new Date());
                    word.setUpdateTime(new Date());
                    return word;
                })
                .collect(Collectors.toList());

        // 批量保存并更新结果映射
        if (!newWords.isEmpty()) {
            this.saveBatch(newWords);
                         newWords.forEach(word -> resultMap.put(word.getErrorWord(), word));
        }

        return resultMap;
    }
} 