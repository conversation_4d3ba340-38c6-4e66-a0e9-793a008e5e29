package cn.dahe.service.impl;

import cn.dahe.dao.CheckContentDao;
import cn.dahe.dao.CheckResultDao;
import cn.dahe.dao.CheckTaskDao;
import cn.dahe.model.dto.ArticleCheckDto;
import cn.dahe.model.dto.ArticleCheckExecuteDto;
import cn.dahe.model.dto.ContentCheckResultDto;
import cn.dahe.entity.*;
import cn.dahe.enums.AuditStatusEnum;
import cn.dahe.enums.CheckStatusEnum;
import cn.dahe.service.*;
import cn.dahe.utils.HtmlTextMapper;
import cn.dahe.utils.SpringUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static cn.dahe.service.impl.CheckResultServiceImpl.getMarkedErrorContext;

/**
 * 文章检查内容服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Service
public class CheckTaskServiceImpl extends ServiceImpl<CheckTaskDao, CheckTask> implements CheckTaskService {

    @Resource
    private ArticleService articleService;
    @Resource
    private WebsiteService websiteService;
    @Resource
    private CheckResultDao checkResultDao;
    @Resource
    private CheckContentDao checkContentDao;






    @Override
    public void updateCheckContentStatus(Long contentId, ContentCheckResultDto checkResult) {
        CheckTask checkContent = new CheckTask().setId(contentId);

        // 2. 根据校对结果更新状态
        if (!checkResult.isSuccess()) {
            // 校对失败
            checkContent.setCheckStatus(CheckStatusEnum.FAILED_RETRY.getCode()); // 检查失败待重试
            checkContent.setFailType(checkResult.getErrorType());
            // 组合失败原因信息
            String failReason = StrUtil.format("失败原因：{}，响应时间：{}ms，详细信息：{}",
                    checkResult.getErrorMessage(),
                    ObjectUtil.defaultIfNull(checkResult.getResponseTime(), 0),
                    checkResult.getErrorDetail()
            );
            checkContent.setFailReason(failReason);
        } else {
            // 校对成功
            checkContent.setCheckStatus(CheckStatusEnum.CHECKED.getCode()); // 已检查
            checkContent.setFailType(null);
            checkContent.setFailReason(null);
            // 设置检查时间
            checkContent.setCheckTime(new Date());
        }

        // 3. 保存更新
        this.updateById(checkContent);
    }

    @Override
    public ArticleCheckExecuteDto initArticleCheckTask(Article article, String title, String content) {
        Long articleId = article.getId();
        Long websiteId = article.getWebsiteId();
        // 1. 清理HTML
        String htmlTitle = HtmlTextMapper.cleanHtml(title);
        String htmlContent = HtmlTextMapper.cleanHtml(content);

        // 2. 提取文本
        HtmlTextMapper.HtmlResult titleResult = HtmlTextMapper.extractText(htmlTitle);
        HtmlTextMapper.HtmlResult contentResult = HtmlTextMapper.extractText(htmlContent);

        // 3. 保存检查需要的初始数据
        CheckTask checkTask = new CheckTask();
        CheckContent checkContent = new CheckContent();
        checkContent.setCompressedTitle(titleResult.getCompressedHtml());
        checkContent.setCompressedContent(contentResult.getCompressedHtml());
        checkContent.setCleanedTitle(titleResult.getPlainText());
        checkContent.setCleanedContent(contentResult.getPlainText());
        checkTask.setContentLength(contentResult.getPlainText().length());
        checkTask.setCheckStatus(CheckStatusEnum.UNCHECKED.getCode());
        Integer checkStrategy = websiteService.getCheckStrategy(websiteId);
        checkTask.setCheckStrategy(checkStrategy);
        this.save(checkTask);
        //  TODO 转载信源更新
        checkContentDao.insert(checkContent.setId(checkTask.getId()));
        articleService.updateArticleCheckRelation(articleId, checkTask);


        return new ArticleCheckExecuteDto().setArticleContentId(checkTask.getId())
                .setCheckTask(checkTask).setCheckContent(checkContent)
                .setTitleResult(titleResult).setContentResult(contentResult);
    }

    @Resource
    private CheckWordService checkWordService;

    @Override
    public void processCheckResults(Long articleId, ArticleCheckExecuteDto checkExecuteDto, ContentCheckResultDto checkResult) {
        Long checkContentId = checkExecuteDto.getArticleContentId();
        //  调用成功则对结果进行保存更新
        if (checkResult.isSuccess()) {
            List<ArticleCheckDto> checkResults = checkResult.getCheckResults();
            HtmlTextMapper.HtmlResult titleResult = checkExecuteDto.getTitleResult();
            HtmlTextMapper.HtmlResult contentResult = checkExecuteDto.getContentResult();
            CheckContent checkContent = checkExecuteDto.getCheckContent();
            // 获取或创建分句列表（根据位置复用）
            List<HtmlTextMapper.SentenceLocation> titleSentences = HtmlTextMapper.splitSentences(checkContent.getCleanedTitle());
            List<HtmlTextMapper.SentenceLocation> contentSentences = HtmlTextMapper.splitSentences(checkContent.getCleanedContent());
            // 更新校对结果的位置信息
            if (CollUtil.isNotEmpty(checkResults)) {
                List<CheckResult> checks = new ArrayList<>();
                Map<String, CheckWord> wordMap = checkWordService.batchGetOrCreateCheckWords(checkResults);
                for (ArticleCheckDto checkDto : checkResults) {
                    CheckResult check = new CheckResult();
                    // check.setArticleId(articleId);
                    check.setCheckId(checkContentId);
                    String errorWord = checkDto.getErrorWord();
                    CheckWord checkWord = wordMap.get(errorWord);
                    check.setWordId(checkWord.getId());
                    // 根据是标题还是正文，使用对应的映射关系更新HTML位置
                    HtmlTextMapper.HtmlWordLocation location;
                    Integer position = checkDto.getPosition();
                    check.setPosition(position);
                    if (checkDto.getArticleLocation() == 1) { // 标题
                        location = HtmlTextMapper.findInHtml(errorWord, position, titleResult);
                        if (location != null) {
                            check.setHtmlPosition(location.getStartPosition());
                            check.setHtmlErrorWord(location.getHtmlErrorWord());
                        }
                    } else { // 正文
                        location = HtmlTextMapper.findInHtml(errorWord, position, contentResult);
                        if (location != null) {
                            check.setHtmlPosition(location.getStartPosition());
                            check.setHtmlErrorWord(location.getHtmlErrorWord());
                        }
                    }
                    // 获取或创建分句列表（根据位置复用）
                    HtmlTextMapper.MarkedErrorContext markedContext;
                    if (checkDto.getArticleLocation() == 1) {
                        markedContext = getMarkedErrorContext(titleSentences, checkWord, position);
                    } else {
                        markedContext = getMarkedErrorContext(contentSentences, checkWord, position);
                    }
                    if (markedContext != null) {
                        check.setContext(markedContext.getContext());
                        check.setMarkedContext(markedContext.getMarkedContext());
                    }
                    check.setAuditStatus(AuditStatusEnum.WAITING_FOR_REVIEW.getType()); // 未审核
                    checks.add(check);
                }
                SpringUtils.getBean(CheckResultService.class).saveBatch(checks);
            }
            //  更新文章表数据
            articleService.updateCheckInfo(articleId, checkContentId);
        }
        // 更新校对状态和结果
        updateCheckContentStatus(checkContentId, checkResult);
    }


} 