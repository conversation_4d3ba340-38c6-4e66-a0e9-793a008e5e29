package cn.dahe.service.impl;

import cn.dahe.dao.ChkUpdateSiteIndexDao;
import cn.dahe.model.dto.PageResult;
import cn.dahe.entity.ChkUpdateSiteIndex;
import cn.dahe.model.query.ChkUpdateSiteIndexQuery;
import cn.dahe.service.ChkUpdateSiteIndexService;
import cn.dahe.model.vo.ChkUpdateSiteIndexVO;
import cn.dahe.model.vo.LoginUserVO;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 首页更新检查Service实现类 - 专注于首页更新检查功能
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Service
public class ChkUpdateSiteIndexServiceImpl extends BaseServiceImpl<ChkUpdateSiteIndexDao, ChkUpdateSiteIndex> implements ChkUpdateSiteIndexService {

    // ==================== 首页更新检查概览 ====================

    @Override
    public Map<String, Object> getOverviewStatistics(ChkUpdateSiteIndexQuery query) {
        try {
            Map<String, Object> result = baseMapper.getOverviewStatistics(query);
            
            // 如果没有数据，返回兜底数据
            if (result == null || result.isEmpty()) {
                return createMockOverviewStatistics();
            }
            
            return result;
        } catch (Exception e) {
            log.error("获取网站概览统计失败，返回兜底数据", e);
            return createMockOverviewStatistics();
        }
    }

    // ==================== 网站详情列表 ====================

    @Override
    public PageResult<ChkUpdateSiteIndexVO> page(ChkUpdateSiteIndexQuery query) {
        try {
            Page<Map<String, Object>> page = new Page<>(query.getPage(), query.getLimit());

            IPage<Map<String, Object>> result = baseMapper.selectPageWithExtInfo(
                    page,
                    StrUtil.isNotBlank(query.getGroupId()) ? query.getGroupId() : null,
                    StrUtil.isNotBlank(query.getGroupName()) ? query.getGroupName() : null,
                    StrUtil.isNotBlank(query.getWebsiteId()) ? query.getWebsiteId() : null,
                    StrUtil.isNotBlank(query.getWebsiteName()) ? query.getWebsiteName() : null,
                    StrUtil.isNotBlank(query.getWebsiteIndexUrl()) ? query.getWebsiteIndexUrl() : null,
                    StrUtil.isNotBlank(query.getBeginTime()) ? query.getBeginTime() : null,
                    StrUtil.isNotBlank(query.getEndTime()) ? query.getEndTime() : null
            );

            // 如果没有数据，返回兜底数据
            if (result.getRecords().isEmpty()) {
                return createMockPageData(query);
            }

            // 转换Map到VO
            List<ChkUpdateSiteIndexVO> voList = new ArrayList<>();
            for (Map<String, Object> map : result.getRecords()) {
                ChkUpdateSiteIndexVO vo = convertMapToVO(map);
                voList.add(vo);
            }

            return new PageResult<ChkUpdateSiteIndexVO>(voList, result.getTotal(), (int) result.getSize(), (int) result.getCurrent());
        } catch (Exception e) {
            log.error("查询网站详情列表失败，返回兜底数据", e);
            return createMockPageData(query);
        }
    }

    @Override
    public ChkUpdateSiteIndexVO get(Long id) {
        try {
            Map<String, Object> result = baseMapper.selectDetailById(id);
            if (result == null) {
                // 返回兜底数据
                return createMockDetailData(id);
            }
            // 转换Map到VO
            return convertMapToVO(result);
        } catch (Exception e) {
            log.error("获取网站详情失败，返回兜底数据", e);
            return createMockDetailData(id);
        }
    }

    @Override
    public PageResult<ChkUpdateSiteIndex> pageDetail(ChkUpdateSiteIndexQuery query) {
        try {
            Page<ChkUpdateSiteIndex> page = new Page<>(query.getPage(), query.getLimit());

            IPage<ChkUpdateSiteIndex> result = baseMapper.selectDetailPage(
                    page,
                    StrUtil.isNotBlank(query.getGroupId()) ? query.getGroupId() : null,
                    StrUtil.isNotBlank(query.getGroupName()) ? query.getGroupName() : null,
                    StrUtil.isNotBlank(query.getWebsiteId()) ? query.getWebsiteId() : null,
                    StrUtil.isNotBlank(query.getWebsiteName()) ? query.getWebsiteName() : null,
                    StrUtil.isNotBlank(query.getWebsiteIndexUrl()) ? query.getWebsiteIndexUrl() : null,
                    StrUtil.isNotBlank(query.getBeginTime()) ? query.getBeginTime() : null,
                    StrUtil.isNotBlank(query.getEndTime()) ? query.getEndTime() : null
            );

            // 如果没有数据，返回兜底数据
            if (result.getRecords().isEmpty()) {
                return createMockDetailPageData(query);
            }

            return PageResult.page(result);
        } catch (Exception e) {
            log.error("查询详细记录列表失败，返回兜底数据", e);
            return createMockDetailPageData(query);
        }
    }


    // ==================== 数据导出 ====================

    @Override
    public void export(ChkUpdateSiteIndexQuery query, LoginUserVO user, HttpServletResponse response) throws IOException {
        try {
            log.info("用户{}导出首页更新检查记录", user.getUserId());

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("首页更新检查记录_" + System.currentTimeMillis(), "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

            // 获取数据
            query.setPage(1);
            query.setLimit(10000); // 导出最多10000条
            PageResult<ChkUpdateSiteIndexVO> pageResult = this.page(query);
            List<ChkUpdateSiteIndexVO> dataList = pageResult.getList();

            // 创建Excel工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("首页更新检查记录");

            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] headers = {"序号", "分组", "网站名称", "网站地址", "首页是否更新", "更新天数", "连续未更新天数", "解析时间"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }

            // 填充数据
            for (int i = 0; i < dataList.size(); i++) {
                Row row = sheet.createRow(i + 1);
                ChkUpdateSiteIndexVO data = dataList.get(i);

                row.createCell(0).setCellValue(i + 1);
                row.createCell(1).setCellValue(data.getGroupName() != null ? data.getGroupName() : "");
                row.createCell(2).setCellValue(data.getWebsiteName() != null ? data.getWebsiteName() : "");
                row.createCell(3).setCellValue(data.getWebsiteIndexUrl() != null ? data.getWebsiteIndexUrl() : "");
                row.createCell(4).setCellValue(data.getStatus() != null ? (data.getStatus() == 1 ? "是" : "否") : "否");
                row.createCell(5).setCellValue(data.getUpdateDays() != null ? String.join(",", data.getUpdateDays()) : "");
                row.createCell(6).setCellValue(data.getContinuousNotUpdateDays() != null ? String.join(",", data.getContinuousNotUpdateDays()) : "");
                row.createCell(7).setCellValue(data.getLastParseTime() != null ?
                    new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(data.getLastParseTime()) : "");
            }

            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // 写入响应流
            workbook.write(response.getOutputStream());
            workbook.close();

        } catch (Exception e) {
            log.error("导出首页更新检查记录失败", e);
            throw new IOException("导出失败：" + e.getMessage());
        }
    }

    // ==================== 兜底数据创建方法 ====================

    /**
     * 创建概览统计兜底数据
     */
    private Map<String, Object> createMockOverviewStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalWebsiteCount", 100);  // 检测网站数
        statistics.put("updatedWebsiteCount", 37); // 更新网站
        statistics.put("notUpdatedWebsiteCount", 63); // 未更新网站
        return statistics;
    }

    /**
     * 创建分页数据兜底数据
     */
    private PageResult<ChkUpdateSiteIndexVO> createMockPageData(ChkUpdateSiteIndexQuery query) {
        List<ChkUpdateSiteIndexVO> mockList = new ArrayList<>();

        String[] websites = {"河南师范大学", "中原工学院", "河南理工大学"};
        String[] urls = {"https://www.henan.edu.cn/", "https://www.zut.edu.cn/", "https://www.hpu.edu.cn/"};
        Integer[] statusArray = {1, 0, 1}; // 1已更新，0未更新

        int limit = query.getLimit() != null ? query.getLimit() : 10;
        for (int i = 1; i <= Math.min(limit, 3); i++) {
            // 根据查询条件过滤mock数据
            boolean shouldInclude = true;

            // 按分组ID过滤（mock数据假设分组ID为1）
            if (StrUtil.isNotBlank(query.getGroupId())) {
                String[] groupIds = query.getGroupId().split(",");
                boolean groupIdMatch = false;
                for (String groupId : groupIds) {
                    if ("1".equals(groupId.trim())) {
                        groupIdMatch = true;
                        break;
                    }
                }
                if (!groupIdMatch) {
                    shouldInclude = false;
                }
            }

            // 按分组名称过滤
            if (StrUtil.isNotBlank(query.getGroupName())) {
                String[] groupNames = query.getGroupName().split(",");
                boolean groupNameMatch = false;
                for (String groupName : groupNames) {
                    if ("高校网站".contains(groupName.trim())) {
                        groupNameMatch = true;
                        break;
                    }
                }
                if (!groupNameMatch) {
                    shouldInclude = false;
                }
            }

            // 按网站ID过滤
            if (StrUtil.isNotBlank(query.getWebsiteId())) {
                String[] websiteIds = query.getWebsiteId().split(",");
                boolean websiteIdMatch = false;
                for (String websiteId : websiteIds) {
                    if (String.valueOf(i).equals(websiteId.trim())) {
                        websiteIdMatch = true;
                        break;
                    }
                }
                if (!websiteIdMatch) {
                    shouldInclude = false;
                }
            }

            // 按网站名称过滤
            if (StrUtil.isNotBlank(query.getWebsiteName())) {
                String[] websiteNames = query.getWebsiteName().split(",");
                boolean websiteNameMatch = false;
                for (String websiteName : websiteNames) {
                    if (websites[i - 1].contains(websiteName.trim())) {
                        websiteNameMatch = true;
                        break;
                    }
                }
                if (!websiteNameMatch) {
                    shouldInclude = false;
                }
            }

            // 按网站URL过滤
            if (StrUtil.isNotBlank(query.getWebsiteIndexUrl())) {
                String[] websiteUrls = query.getWebsiteIndexUrl().split(",");
                boolean urlMatch = false;
                for (String url : websiteUrls) {
                    if (urls[i - 1].contains(url.trim())) {
                        urlMatch = true;
                        break;
                    }
                }
                if (!urlMatch) {
                    shouldInclude = false;
                }
            }

            if (!shouldInclude) {
                continue;
            }

            ChkUpdateSiteIndexVO vo = new ChkUpdateSiteIndexVO();
            vo.setId((long) (i + 5)); // 从6开始，对应原型图
            vo.setGroupName("高校网站"); // 分组名称
            vo.setWebsiteName(websites[i - 1]); // 网站名称
            vo.setWebsiteIndexUrl(urls[i - 1]); // 网站地址
            vo.setStatus(statusArray[i - 1]); // 首页是否更新：0未更新，1已更新

            // 更新天数列表
            List<String> updateDays = new ArrayList<>();
            if (statusArray[i - 1] == 1) {
                updateDays.add("2025-07-30");
                updateDays.add("2025-07-29");
            }
            vo.setUpdateDays(updateDays);

            // 连续未更新天数列表
            List<String> continuousNotUpdateDays = new ArrayList<>();
            if (statusArray[i - 1] == 0) {
                continuousNotUpdateDays.add("2025-07-30");
                continuousNotUpdateDays.add("2025-07-29");
                continuousNotUpdateDays.add("2025-07-28");
            }
            vo.setContinuousNotUpdateDays(continuousNotUpdateDays);

            // 解析时间
            vo.setLastParseTime(new Date(System.currentTimeMillis() - i * 24 * 60 * 60 * 1000L));

            mockList.add(vo);
        }

        return new PageResult<>(mockList, (long) mockList.size(), limit, query.getPage() != null ? query.getPage() : 1);
    }

    /**
     * 创建详情兜底数据
     */
    private ChkUpdateSiteIndexVO createMockDetailData(Long id) {
        ChkUpdateSiteIndexVO vo = new ChkUpdateSiteIndexVO();
        vo.setId(id);
        vo.setGroupName("高校网站"); // 分组名称
        vo.setWebsiteName("河南师范大学"); // 网站名称
        vo.setWebsiteIndexUrl("https://www.henan.edu.cn/"); // 网站地址
        vo.setStatus(1); // 首页是否更新：1已更新

        // 更新天数列表
        List<String> updateDays = new ArrayList<>();
        updateDays.add("2025-07-30");
        updateDays.add("2025-07-29");
        vo.setUpdateDays(updateDays);

        // 连续未更新天数列表（已更新的网站为空）
        vo.setContinuousNotUpdateDays(new ArrayList<>());

        vo.setLastParseTime(new Date()); // 解析时间
        return vo;
    }

    /**
     * 创建详细记录分页数据兜底数据
     */
    private PageResult<ChkUpdateSiteIndex> createMockDetailPageData(ChkUpdateSiteIndexQuery query) {
        List<ChkUpdateSiteIndex> mockList = new ArrayList<>();

        String[] websites = {"河南师范大学", "中原工学院", "河南理工大学"};
        String[] urls = {"https://www.henan.edu.cn/", "https://www.zut.edu.cn/", "https://www.hpu.edu.cn/"};
        String[] titles = {"学校召开2025年工作会议", "关于寒假放假安排的通知", "学术报告：人工智能发展趋势"};

        int limit = query.getLimit() != null ? query.getLimit() : 10;
        for (int i = 1; i <= Math.min(limit, 5); i++) {
            ChkUpdateSiteIndex entity = new ChkUpdateSiteIndex();
            entity.setId((long) i);
            entity.setGroupId(1);
            entity.setGroupName("高校网站");
            entity.setWebsiteId(i % 3 + 1);
            entity.setWebsiteName(websites[i % 3]);
            entity.setWebsiteIndexUrl(urls[i % 3]);
            entity.setArticleTitle(titles[i % 3]);
            entity.setArticleContent("这是文章内容的详细描述，包含了网站首页的新闻内容信息...");
            entity.setArticleUrl(urls[i % 3] + "news/" + i);
            entity.setArticlePublishTime(new Date(System.currentTimeMillis() - i * 24 * 60 * 60 * 1000L));
            entity.setParseTime(new Date(System.currentTimeMillis() - i * 12 * 60 * 60 * 1000L));
            entity.setCreateTime(new Date(System.currentTimeMillis() - i * 6 * 60 * 60 * 1000L));

            mockList.add(entity);
        }

        return new PageResult<>(mockList, (long) mockList.size(), limit, query.getPage() != null ? query.getPage() : 1);
    }

    /**
     * 将Map转换为VO对象
     */
    private ChkUpdateSiteIndexVO convertMapToVO(Map<String, Object> map) {
        ChkUpdateSiteIndexVO vo = new ChkUpdateSiteIndexVO();

        // 基本字段映射
        vo.setId(map.get("id") != null ? Long.valueOf(map.get("id").toString()) : null);
        vo.setGroupName((String) map.get("groupName"));
        vo.setWebsiteName((String) map.get("websiteName"));
        vo.setWebsiteIndexUrl((String) map.get("websiteIndexUrl"));
        vo.setStatus(map.get("status") != null ? Integer.valueOf(map.get("status").toString()) : null);

        // 处理时间类型转换
        vo.setLastParseTime(convertToDate(map.get("lastParseTime")));

        // 处理字符串到List的转换
        String updateDaysStr = (String) map.get("updateDaysStr");
        if (StrUtil.isNotBlank(updateDaysStr)) {
            // 去除空白字符并分割
            String[] days = updateDaysStr.trim().split(",");
            List<String> daysList = new ArrayList<>();
            for (String day : days) {
                if (StrUtil.isNotBlank(day)) {
                    daysList.add(day.trim());
                }
            }
            vo.setUpdateDays(daysList);
        } else {
            vo.setUpdateDays(new ArrayList<>());
        }

        String continuousNotUpdateDaysStr = (String) map.get("continuousNotUpdateDaysStr");
        if (StrUtil.isNotBlank(continuousNotUpdateDaysStr)) {
            // 去除空白字符并分割
            String[] days = continuousNotUpdateDaysStr.trim().split(",");
            List<String> daysList = new ArrayList<>();
            for (String day : days) {
                if (StrUtil.isNotBlank(day)) {
                    daysList.add(day.trim());
                }
            }
            vo.setContinuousNotUpdateDays(daysList);
        } else {
            vo.setContinuousNotUpdateDays(new ArrayList<>());
        }

        return vo;
    }

    /**
     * 将时间对象转换为Date类型
     * 处理LocalDateTime和Date类型的转换
     */
    private Date convertToDate(Object timeObj) {
        if (timeObj == null) {
            return null;
        }

        if (timeObj instanceof Date) {
            return (Date) timeObj;
        } else if (timeObj instanceof LocalDateTime) {
            LocalDateTime localDateTime = (LocalDateTime) timeObj;
            return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
        } else {
            // 如果是其他类型，返回null
            return null;
        }
    }

}
