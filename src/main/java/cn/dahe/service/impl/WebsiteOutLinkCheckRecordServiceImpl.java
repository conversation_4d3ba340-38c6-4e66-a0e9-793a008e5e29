package cn.dahe.service.impl;

import cn.dahe.dao.WebsiteOutLinkCheckRecordDao;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.WebsiteAccessOverviewDto;
import cn.dahe.model.dto.WebsiteUpdateStatsDto;
import cn.dahe.entity.Website;
import cn.dahe.entity.WebsiteOutLinkCheckRecord;
import cn.dahe.model.query.WebsiteOutLinkCheckRecordQuery;
import cn.dahe.service.WebsiteOutLinkCheckRecordService;
import cn.dahe.service.WebsiteService;
import cn.dahe.utils.ListUtils;
import cn.dahe.utils.NumberUtils;
import cn.dahe.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 *
 */
@Service
@AllArgsConstructor
public class WebsiteOutLinkCheckRecordServiceImpl extends BaseServiceImpl<WebsiteOutLinkCheckRecordDao, WebsiteOutLinkCheckRecord> implements WebsiteOutLinkCheckRecordService {

    @Resource
    private WebsiteService websiteService;


    @Override
    public PageResult<WebsiteOutLinkCheckRecord> pageCheckRecordDetail(WebsiteOutLinkCheckRecordQuery query) {
        IPage<WebsiteOutLinkCheckRecord> dataPage = baseMapper.selectPage(new Page(query.getPage(), query.getLimit()), getWrapper(query));
        return new PageResult<>(dataPage.getRecords(), dataPage);
    }


    @Override
    public WebsiteUpdateStatsDto totalOverview(WebsiteOutLinkCheckRecordQuery query) {
        List<Website> websiteList = websiteService.listByIds(ListUtils.transferIdsToList(query.getWebId()));
        List<WebsiteAccessOverviewDto> overviewDtoList = new ArrayList<>();

        for (Website website : websiteList) {
            WebsiteAccessOverviewDto dto = buildWebsiteAccessOverview(website, query);
            overviewDtoList.add(dto);
        }
        //检测网站数
        int totalSites = overviewDtoList.size();


        // 按 errorCount 是否为0 分区：正常站点 & 出错站点
        Map<Boolean, List<WebsiteAccessOverviewDto>> partitioned = overviewDtoList.stream()
                .collect(Collectors.partitioningBy(dto -> dto.getErrorCount() == 0));
        //外链正常网站
        int normalSites = partitioned.getOrDefault(true, Collections.emptyList()).size();
        //外链异常网站
        int errorSites = totalSites - normalSites;

        String errorRate = NumberUtils.calculateRatioPercentage(errorSites, totalSites, 2);

        WebsiteUpdateStatsDto statsDto = new WebsiteUpdateStatsDto();
        statsDto.setTotalSites(totalSites);
        statsDto.setNormalSites(normalSites);
        statsDto.setErrorSites(errorSites);
        statsDto.setErrorRate(errorRate);

        return statsDto;
    }

    @Override
    public PageResult<WebsiteAccessOverviewDto> pageWebsiteStats(WebsiteOutLinkCheckRecordQuery query) {
        PageResult<Website> websitePageResult = websiteService.pageByWebIds(query.getWebId(), query.getPage(), query.getLimit());
        List<WebsiteAccessOverviewDto> overviewDtoList = new ArrayList<>();

        for (Website website : websitePageResult.getList()) {
            WebsiteAccessOverviewDto dto = buildWebsiteAccessOverview(website, query);
            overviewDtoList.add(dto);
        }

        return new PageResult<>(overviewDtoList, websitePageResult.getTotal(), websitePageResult.getPageSize(), websitePageResult.getPage());
    }

    @Override
    public boolean updateFilterByWebIdAndLinkUrl(int filter, int webId, String linkurl) {
        UpdateWrapper<WebsiteOutLinkCheckRecord> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("web_id", webId);
        updateWrapper.eq("link_url", linkurl);
        updateWrapper.set("filter", filter);
        return update(updateWrapper);
    }


    private WebsiteAccessOverviewDto buildWebsiteAccessOverview(Website website, WebsiteOutLinkCheckRecordQuery query) {
        WebsiteAccessOverviewDto dto = new WebsiteAccessOverviewDto();
        dto.setId(website.getId());
        dto.setWebUrl(website.getWebUrl());
        dto.setWebName(website.getWebName());

        //总的死链数
        int totalCount = baseMapper.countByFilters(
                String.valueOf(website.getId()),
                query.getSourcePage(),
                query.getDeadLinkUrl(),
                query.getHttpCode(),
                query.getBeginTime(),
                query.getEndTime()
        );

        // 总的死链数
        int errorCount = totalCount;

        String errorRate = NumberUtils.calculateRatioPercentage(errorCount, totalCount, 2);

        dto.setTotalCount(totalCount);
        dto.setErrorCount(errorCount);
        dto.setErrorRate(errorRate);

        return dto;
    }


    private QueryWrapper<WebsiteOutLinkCheckRecord> getWrapper(WebsiteOutLinkCheckRecordQuery query) {
        QueryWrapper<WebsiteOutLinkCheckRecord> queryWrapper = Wrappers.query();
        if (StringUtils.isNotBlank(query.getWebId())) {
            queryWrapper.in("web_id", ListUtils.transferIdsToList(query.getWebId()));
        }
        if (StringUtils.isNotBlank(query.getBeginTime())) {
            queryWrapper.ge("check_time", query.getBeginTime());
        }
        if (StringUtils.isNotBlank(query.getEndTime())) {
            queryWrapper.le("check_time", query.getEndTime());
        }
        if (StringUtils.isNotBlank(query.getHttpCode())) {
            queryWrapper.eq("http_code", query.getHttpCode());
        }
        // 死链-模糊搜索
        if (StringUtils.isNotBlank(query.getDeadLinkUrlLike())) {
            queryWrapper.like("dead_link_url", query.getDeadLinkUrlLike());
        }
        // 死链-准确搜索
        if (StringUtils.isNotBlank(query.getDeadLinkUrl())) {
            queryWrapper.eq("dead_link_url", query.getDeadLinkUrl());
        }
        // 来源页面
        if (StringUtils.isNotBlank(query.getSourcePage())) {
            queryWrapper.eq("source_page", query.getSourcePage());
        }
        if (StringUtils.isNotBlank(query.getDeadLinkType())) {
            queryWrapper.eq("type", query.getDeadLinkType());
        }
        if (StringUtils.isNotBlank(query.getLatest())) {
            queryWrapper.eq("latest", query.getLatest());
        }
        if (StringUtils.isNotBlank(query.getFilter())) {
            queryWrapper.eq("filter", query.getFilter());
        }
        queryWrapper.orderByDesc("check_time");
        return queryWrapper;
    }


}