package cn.dahe.service.impl;

import cn.dahe.dao.UserDao;
import cn.dahe.dao.UserSiteColumnDao;
import cn.dahe.model.dto.PageResult;
import cn.dahe.entity.UserSiteColumn;
import cn.dahe.model.query.UserSiteColumnQuery;
import cn.dahe.service.UserSiteColumnService;
import cn.dahe.utils.FanCollectUtil;
import cn.dahe.model.vo.UserSiteColumnVO;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户站点栏目关联Service实现
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserSiteColumnServiceImpl implements UserSiteColumnService {

    private final UserSiteColumnDao userSiteColumnDao;
    private final UserDao userDao;

    @Override
    public PageResult<UserSiteColumnVO> page(UserSiteColumnQuery query) {
        Page<UserSiteColumn> page = new Page<>(query.getPage(), query.getLimit());
        
        LambdaQueryWrapper<UserSiteColumn> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(query.getUserId() != null, UserSiteColumn::getUserId, query.getUserId())
               .like(StrUtil.isNotBlank(query.getUserName()), UserSiteColumn::getUserName, query.getUserName())
               .eq(query.getSiteId() != null, UserSiteColumn::getSiteId, query.getSiteId())
               .like(StrUtil.isNotBlank(query.getSiteName()), UserSiteColumn::getSiteName, query.getSiteName())
               .eq(query.getColumnId() != null, UserSiteColumn::getColumnId, query.getColumnId())
               .like(StrUtil.isNotBlank(query.getColumnName()), UserSiteColumn::getColumnName, query.getColumnName())
               .like(StrUtil.isNotBlank(query.getCreateByUserName()), UserSiteColumn::getCreateByUserName, query.getCreateByUserName())
               .ge(StrUtil.isNotBlank(query.getCreateBeginTime()), UserSiteColumn::getCreateTime, query.getCreateBeginTime())
               .le(StrUtil.isNotBlank(query.getCreateEndTime()), UserSiteColumn::getCreateTime, query.getCreateEndTime())
               .orderByDesc(UserSiteColumn::getCreateTime);

        IPage<UserSiteColumn> result = userSiteColumnDao.selectPage(page, wrapper);
        
        List<UserSiteColumnVO> voList = result.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return new PageResult<UserSiteColumnVO>(voList, result.getTotal(), (int) result.getSize(), (int) result.getCurrent());
    }

    @Override
    public JSONObject getSiteList(String siteName) {
        try {
            return FanCollectUtil.searchSitesByName(siteName);
        } catch (Exception e) {
            log.error("获取站点列表失败", e);
            return createErrorResponse("获取站点列表失败", e.getMessage());
        }
    }

    @Override
    public JSONObject getSiteColumns(Integer siteId) {
        try {
            return FanCollectUtil.getSiteColumns(siteId);
        } catch (Exception e) {
            log.error("获取站点栏目列表失败", e);
            return createErrorResponse("获取站点栏目列表失败", e.getMessage());
        }
    }

    @Override
    public List<UserSiteColumnVO> getUserAssignedSites(Integer userId) {
        List<UserSiteColumn> assignments = userSiteColumnDao.selectByUserId(userId);
        
        // 按站点分组，去重
        return assignments.stream()
                .collect(Collectors.groupingBy(UserSiteColumn::getSiteId))
                .values()
                .stream()
                .map(list -> {
                    UserSiteColumn first = list.get(0);
                    UserSiteColumnVO vo = new UserSiteColumnVO();
                    vo.setUserId(first.getUserId());
                    vo.setSiteId(first.getSiteId());
                    vo.setSiteName(first.getSiteName());
                    vo.setSiteUrl(first.getSiteUrl());
                    return vo;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<UserSiteColumnVO> getUserAssignedColumns(Integer userId, Integer siteId) {
        List<UserSiteColumn> assignments = userSiteColumnDao.selectByUserIdAndSiteId(userId, siteId);
        
        return assignments.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignSiteColumns(Integer userId, List<SiteColumnAssignVO> assignments, String currentUserId, String currentUserName) {
        try {
            // 获取用户信息（这里可以从数据库查询用户姓名）
            String userName = getUserNameById(userId);

            for (SiteColumnAssignVO assignment : assignments) {
                for (ColumnAssignVO column : assignment.getColumns()) {
                    // 检查是否已存在
                    int count = userSiteColumnDao.countByUserSiteColumn(userId, assignment.getSiteId(), column.getColumnId());
                    if (count > 0) {
                        log.warn("用户{}的站点{}栏目{}已存在，跳过", userId, assignment.getSiteId(), column.getColumnId());
                        continue;
                    }

                    // 创建新记录
                    UserSiteColumn entity = new UserSiteColumn();
                    entity.setUserId(userId);
                    entity.setUserName(userName);
                    entity.setSiteId(assignment.getSiteId());
                    entity.setSiteName(assignment.getSiteName());
                    entity.setSiteUrl(assignment.getSiteUrl());
                    entity.setColumnId(column.getColumnId());
                    entity.setColumnName(column.getColumnName());
                    entity.setColumnUrl(column.getColumnUrl());

                    // 设置创建信息
                    entity.setCreateTime(new Date());
                    entity.setCreateByUserId(currentUserId != null ? currentUserId : "system");
                    entity.setCreateByUserName(currentUserName != null ? currentUserName : "系统");
                    entity.setUpdateTime(new Date());
                    entity.setUpdateByUserId(currentUserId != null ? currentUserId : "system");
                    entity.setUpdateByUserName(currentUserName != null ? currentUserName : "系统");

                    userSiteColumnDao.insert(entity);
                }
            }
            return true;
        } catch (Exception e) {
            log.error("分配站点栏目失败", e);
            throw new RuntimeException("分配站点栏目失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeUserSiteAssignments(Integer userId, List<Integer> siteIds) {
        try {
            int count = userSiteColumnDao.deleteByUserIdAndSiteIds(userId, siteIds);
            log.info("删除用户{}的{}个站点分配，共删除{}条记录", userId, siteIds.size(), count);
            return true;
        } catch (Exception e) {
            log.error("删除用户站点分配失败", e);
            throw new RuntimeException("删除用户站点分配失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeUserSiteColumnAssignments(Integer userId, Integer siteId, List<Integer> columnIds) {
        try {
            int count = userSiteColumnDao.deleteByUserIdAndSiteIdAndColumnIds(userId, siteId, columnIds);
            log.info("删除用户{}站点{}的{}个栏目分配，共删除{}条记录", userId, siteId, columnIds.size(), count);
            return true;
        } catch (Exception e) {
            log.error("删除用户站点栏目分配失败", e);
            throw new RuntimeException("删除用户站点栏目分配失败: " + e.getMessage());
        }
    }

    /**
     * 转换为VO对象
     */
    private UserSiteColumnVO convertToVO(UserSiteColumn entity) {
        UserSiteColumnVO vo = new UserSiteColumnVO();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }

    /**
     * 创建错误响应
     */
    private JSONObject createErrorResponse(String message, String detail) {
        JSONObject response = new JSONObject();
        response.put("success", false);
        response.put("message", message);
        response.put("detail", detail);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }

    /**
     * 根据用户ID获取用户姓名
     * TODO: 这里应该调用用户服务获取真实的用户姓名
     */
    private String getUserNameById(Integer userId) {
        // 这里应该调用用户服务获取用户姓名
        // 暂时返回默认值
        return userDao.selectById(userId).getUserName();
    }

    /**
     * 为用户分配站点栏目（兼容性方法）
     *
     * @param userId 用户ID
     * @param assignments 分配信息列表
     * @return 分配结果
     */
    public boolean assignSiteColumns(Integer userId, List<SiteColumnAssignVO> assignments) {
        return assignSiteColumns(userId, assignments, "system", "系统");
    }
}
