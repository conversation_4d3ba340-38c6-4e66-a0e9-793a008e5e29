package cn.dahe.service.impl;

import cn.dahe.common.auth.SecurityUtils;
import cn.dahe.dao.AttachmentDao;
import cn.dahe.model.dto.PageResult;
import cn.dahe.entity.Attachment;
import cn.dahe.model.query.AttachmentCheckQuery;
import cn.dahe.service.AttachmentService;
import cn.dahe.service.CheckResultService;
import cn.dahe.utils.ExcelExportUtil;
import cn.dahe.model.vo.AttachmentCheckVO;
import cn.dahe.model.vo.LoginUserVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 附件检查Service实现类 - 对应原型图功能
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Service
public class AttachmentServiceImpl extends BaseServiceImpl<AttachmentDao, Attachment> implements AttachmentService {
    @Resource
    private CheckResultService checkResultService;

    @Override
    public PageResult<AttachmentCheckVO> pageAttachmentAndCheckResults(AttachmentCheckQuery query) {
        Page<AttachmentCheckVO> page = Page.of(query.getPage(), query.getLimit());
        IPage<AttachmentCheckVO> result = this.getBaseMapper().pageAttachmentInfo(page, query);
        return PageResult.page(result);
    }

    @Override
    public AttachmentCheckVO getAttachmentAndCheckResults(Long attachmentId, AttachmentCheckQuery query) {
        AttachmentCheckVO attachmentInfo = baseMapper.getAttachmentInfo(attachmentId);
        checkResultService.markCheckContentWithHtml(attachmentInfo, query);
        return attachmentInfo;
    }

    // ==================== 数据导出 ====================

    @Resource
    private HttpServletResponse httpServletResponse;

    @Override
    public void export(AttachmentCheckQuery query) throws IOException {
        try {
            LoginUserVO user = SecurityUtils.getLoginUser();
            log.info("用户{}导出附件检查记录", user.getUserId());

            // 获取数据
            query.setPage(1);
            query.setLimit(10000); // 导出最多10000条
            PageResult<AttachmentCheckVO> pageResult = this.pageAttachmentAndCheckResults(query);
            List<AttachmentCheckVO> dataList = pageResult.getList();

            // 定义表头
            String[] headers = {"序号", "来源网站", "附件名称", "附件链接", "附件类型", "来源页面", "来源页面链接", "发布时间", "检测时间"};

            // 使用通用导出工具类
            ExcelExportUtil.exportToExcel(
                    httpServletResponse,
                    "附件检查记录",
                    "附件检查记录",
                    headers,
                    dataList,
                    data -> {
                        String[] rowData = new String[headers.length];
                        rowData[0] = String.valueOf(dataList.indexOf(data) + 1); // 序号
                        rowData[1] = ExcelExportUtil.safeString(data.getWebsiteName()); // 来源网站
                        rowData[2] = ExcelExportUtil.safeString(data.getAttachmentName()); // 附件名称
                        rowData[3] = ExcelExportUtil.safeString(data.getAttachmentUrl()); // 附件链接
                        rowData[4] = ExcelExportUtil.safeString(data.getAttachmentType()); // 附件类型
                        rowData[5] = ExcelExportUtil.safeString(data.getSourceName()); // 来源页面
                        rowData[6] = ExcelExportUtil.safeString(data.getSourceUrl()); // 来源页面链接
                        rowData[7] = ExcelExportUtil.formatDate(data.getPubTime()); // 发布时间
                        rowData[8] = ExcelExportUtil.formatDate(data.getCheckTime()); // 检测时间
                        return rowData;
                    }
            );

        } catch (Exception e) {
            log.error("导出附件检查记录失败", e);
            throw new IOException("导出失败：" + e.getMessage());
        }
    }

}
