package cn.dahe.service.impl;

import cn.dahe.dao.CheckResultDao;
import cn.dahe.entity.CheckContent;
import cn.dahe.entity.CheckResult;
import cn.dahe.entity.CheckTask;
import cn.dahe.entity.CheckWord;
import cn.dahe.enums.ArticleLocationEnum;
import cn.dahe.enums.AuditStatusEnum;
import cn.dahe.model.dto.ArticleCheckDto;
import cn.dahe.model.dto.ContentCheckResultDto;
import cn.dahe.model.dto.Result;
import cn.dahe.model.query.CheckResultQuery;
import cn.dahe.model.vo.CheckResultVO;
import cn.dahe.model.vo.CheckVO;
import cn.dahe.model.vo.ErrorLevelCheckResultCountVO;
import cn.dahe.service.*;
import cn.dahe.utils.HtmlTextMapper;
import cn.dahe.utils.HttpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Element;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.SocketTimeoutException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 内容错误详情Service实现类
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Slf4j
@Service
public class CheckResultServiceImpl extends BaseServiceImpl<CheckResultDao, CheckResult> implements CheckResultService {


    @Override
    public Boolean updateAuditStatus(Long articleId, List<Long> resultIds, AuditStatusEnum auditStatus) {
        //  TODO 检查resultIds和articleId的关系
        LambdaUpdateWrapper<CheckResult> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(CheckResult::getAuditStatus, auditStatus.getType());
        updateWrapper.in(CheckResult::getId, resultIds);
        return this.update(updateWrapper);
    }

    @Override
    public <T extends CheckVO, U extends CheckResultQuery> void markCheckContentWithHtml(T vo, U
            query) {
        Long taskId = vo.getTaskId();
        List<CheckResultVO> errorObjs = this.getBaseMapper().listCheckResultByTaskId(taskId, query);
        vo.setErrorObjs(errorObjs);

        // 计算错误级别统计
        ErrorLevelCheckResultCountVO errorLevelCheckResultCountVO = calculateErrorLevelStats(errorObjs);
        vo.setErrorLevelCheckResultCountStats(errorLevelCheckResultCountVO);

        if (CollUtil.isNotEmpty(errorObjs)) {
            // 按位置分组错误
            Map<ArticleLocationEnum, List<CheckResultVO>> errorMap = errorObjs.stream()
                    .collect(Collectors.groupingBy(
                            e -> ArticleLocationEnum.getByValue(e.getArticleLocation())
                    ));

            // 处理标题
            String[] markedTitles = markErrorsInContent(
                    vo.getHtmlTitle(),
                    vo.getCleanedTitle(),
                    errorMap.getOrDefault(ArticleLocationEnum.TITLE, Collections.emptyList()),
                    true  // 是标题
            );
            vo.setMarkedHtmlTitle(markedTitles[0]);
            vo.setMarkedCleanTitle(markedTitles[1]);

            // 处理正文
            String[] markedContents = markErrorsInContent(
                    vo.getHtmlContent(),
                    vo.getCleanedContent(),
                    errorMap.getOrDefault(ArticleLocationEnum.CONTENT, Collections.emptyList()),
                    false  // 不是标题
            );
            vo.setMarkedHtmlContent(markedContents[0]);
            vo.setMarkedCleanContent(markedContents[1]);
        }
    }


    @Value("${content-check.api.url}")
    private String checkApiUrl;

    @Value("${content-check.api.timeout}")
    private int apiTimeout;

    @Value("${spring.profiles.active}")
    private String env;
    @Resource
    private SimCheckService simCheckService;

    @Override
    public ContentCheckResultDto executeContentCheck(CheckTask checkTask, CheckContent checkContent) {
        //  检测策略
        Integer checkStrategy = checkTask.getCheckStrategy();
        String title = checkContent.getCleanedTitle();
        String content = checkContent.getCleanedContent();
        //  TODO 根据检索策略调整检索结果
        if (env.equals("dev")) {
            long startTime = System.currentTimeMillis();
            List<ArticleCheckDto> articleChecks = simCheckService.simulateCheck(title, content);
            long responseTime = System.currentTimeMillis() - startTime;
            return ContentCheckResultDto.success(articleChecks, responseTime);
        }
        // 构建请求参数
        Map<String, Object> params = new HashMap<>();
        params.put("title", title);
        params.put("content", content);

        long startTime = System.currentTimeMillis();
        try {
            // 使用HttpUtil发送请求
            String response = HttpUtil.post(checkApiUrl, params, apiTimeout);
            long responseTime = System.currentTimeMillis() - startTime;

            // 解析响应
            try {
                Result<?> result = JSON.parseObject(response, Result.class);
                // 检查响应状态
                if (result == null) {
                    return ContentCheckResultDto.apiError("接口返回为空", null);
                }
                if (!result.isSuccess()) {
                    return ContentCheckResultDto.apiError(
                            result.getMsg(),
                            String.format("错误码：%s，响应信息：%s", result.getCode(), result.getMsg())
                    );
                }
                JSONObject jsonObject = JSON.parseObject(response);
                List<ArticleCheckDto> checks = jsonObject.getJSONArray("data").toJavaList(ArticleCheckDto.class);
                // 返回成功结果
                return ContentCheckResultDto.success(checks, responseTime);
            } catch (Exception e) {
                return ContentCheckResultDto.apiError(
                        "响应解析失败",
                        StrUtil.format("异常信息：{}，响应数据：\n{}", e.getMessage(), response)
                );
            }
        } catch (SocketTimeoutException e) {
            log.error("调用校对服务超时", e);
            return ContentCheckResultDto.timeoutError(
                    "调用校对服务超时",
                    String.format("请求耗时超过%d毫秒", apiTimeout)
            );
        } catch (Exception e) {
            log.error("调用校对服务失败", e);
            return ContentCheckResultDto.localError(
                    "调用校对服务失败",
                    ExceptionUtil.stacktraceToString(e)
            );
        }
    }

    @Resource
    private CheckWordService checkWordService;


    /**
     * 获取并标记错词的上下文
     *
     * @param sentences 已分好的句子列表
     * @return 包含原始上下文和标记后上下文的对象
     */
    public static HtmlTextMapper.MarkedErrorContext getMarkedErrorContext(List<HtmlTextMapper.SentenceLocation> sentences, CheckWord checkResult, Integer position) {
        String errorWord = checkResult.getErrorWord();
        // 1. 获取错词上下文
        HtmlTextMapper.ErrorWordContext errorContext = HtmlTextMapper.getErrorWordContext(sentences, position, errorWord.length());
        if (errorContext == null) {
            return null;
        }
        // 2. 在上下文中标记错词
        StringBuilder context = new StringBuilder(errorContext.getContext());
        int relativePos = errorContext.getRelativePosition();
        Element errorRemarkSpan = new Element("span")
                .attr("class", StrUtil.format("sensitive_lv{}_remark", checkResult.getErrorLevel()));
        String remarkText = StrUtil.format("{}({};{};{})", errorWord, CheckErrorLevelService.getLevelName(checkResult.getErrorLevel()),
                CheckErrorTypeService.getTypeName(checkResult.getThirdErrorType() == null ? checkResult.getSecondErrorType() : checkResult.getThirdErrorType()),
                StrUtil.isBlankIfStr(checkResult.getSuggestWord()) ? "" : StrUtil.format("建议改为:{};", checkResult.getSuggestWord()));
        String errorRemarkTag = errorRemarkSpan.text(remarkText).outerHtml();
        String markedContext = context.replace(relativePos, relativePos + errorWord.length(), errorRemarkTag).toString();

        return new HtmlTextMapper.MarkedErrorContext(
                errorContext.getContext(),
                markedContext,
                errorContext.getContextStart(),
                errorContext.getContextEnd()
        );
    }


    /**
     * 计算错误级别统计
     *
     * @param errorObjs 错误对象列表
     * @return ErrorLevelCheckCountVO 错误级别统计对象
     */
    private ErrorLevelCheckResultCountVO calculateErrorLevelStats(List<CheckResultVO> errorObjs) {
        ErrorLevelCheckResultCountVO stats = new ErrorLevelCheckResultCountVO();
        if (CollUtil.isEmpty(errorObjs)) {
            return stats;
        }

        stats.setCheckResultCount(errorObjs.size());

        // 使用Stream API按错误级别分组统计
        Map<Integer, Long> levelCounts = errorObjs.stream()
                .collect(Collectors.groupingBy(
                        CheckResultVO::getErrorLevel,
                        Collectors.counting()
                ));

        // 设置各级别错误数量
        stats.setLv1CheckResultCount(levelCounts.getOrDefault(1, 0L).intValue());
        stats.setLv2CheckResultCount(levelCounts.getOrDefault(2, 0L).intValue());
        stats.setLv3CheckResultCount(levelCounts.getOrDefault(3, 0L).intValue());
        stats.setLv4CheckResultCount(levelCounts.getOrDefault(4, 0L).intValue());
        stats.setLv5CheckResultCount(levelCounts.getOrDefault(5, 0L).intValue());

        return stats;
    }

    /**
     * 在内容中标记错误
     *
     * @param htmlContent  HTML内容
     * @param plainContent 纯文本内容
     * @param errors       错误列表
     * @param isTitle      是否是标题
     * @return 标记后的内容数组 [标记后的HTML内容, 标记后的纯文本内容]
     */
    private String[] markErrorsInContent(String htmlContent, String plainContent, List<CheckResultVO> errors, boolean isTitle) {
        if (StringUtils.isBlank(htmlContent) || StringUtils.isBlank(plainContent) || CollUtil.isEmpty(errors)) {
            return new String[]{htmlContent, plainContent};
        }

        // 按照位置倒序排序，这样从后向前处理就不会影响前面的位置
        errors.sort((a, b) -> b.getPosition().compareTo(a.getPosition()));

        StringBuilder htmlResult = new StringBuilder(htmlContent);
        StringBuilder plainResult = new StringBuilder(plainContent);
        // 从错误总数开始倒序计数
        int totalErrors = errors.size();

        for (CheckResultVO error : errors) {
            Element errorWordSpan = new Element("span")
                    .id(StrUtil.format("sensitive_check_{}", error.getResultId()))
                    .attr("class", StrUtil.format("sensitive_lv{}_word", error.getErrorLevel()))
                    .attr("data-check-id", error.getResultId().toString());
            Element errorRemarkSpan = new Element("span")
                    .attr("class", StrUtil.format("sensitive_lv{}_remark", error.getErrorLevel()))
                    .attr("data-check-id", error.getResultId().toString());
            String remarkText = StrUtil.format("({}错误标注第{}处;{};{};{})", isTitle ? "标题" : "正文", totalErrors--, error.getErrorLevelName(),
                    error.getErrorTypeName(), StrUtil.isBlankIfStr(error.getSuggestWord()) ? "" : StrUtil.format("建议改为:{};", error.getSuggestWord()));
            String errorRemarkTag = errorRemarkSpan.text(remarkText).outerHtml();
            // 处理HTML内容
            String htmlErrorWord = error.getHtmlErrorWord();
            Integer htmlPos = error.getHtmlPosition();
            // 找到错误词在HTML中的实际结束位置
            int htmlEndPos = htmlPos + htmlErrorWord.length();
            htmlResult.replace(htmlPos, htmlEndPos, errorWordSpan.html(htmlErrorWord).outerHtml() + errorRemarkTag);

            // 处理纯文本内容
            String errorWord = error.getErrorWord();
            Integer plainPos = error.getPosition();
            // 找到错误词在HTML中的实际结束位置
            int plainEndPos = plainPos + errorWord.length();
            plainResult.replace(plainPos, plainEndPos, errorWordSpan.text(errorWord).outerHtml() + errorRemarkTag);
        }

        return new String[]{htmlResult.toString(), plainResult.toString()};
    }


}