package cn.dahe.service.impl;

import cn.dahe.dao.WebsiteDao;
import cn.dahe.dao.WebsiteGroupDao;
import cn.dahe.model.dto.Result;
import cn.dahe.entity.Website;
import cn.dahe.entity.WebsiteGroup;
import cn.dahe.service.WebsiteGroupService;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.model.vo.WebsiteGroupVO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 网站分组Service实现类
 */
@Service
public class WebsiteGroupServiceImpl extends BaseServiceImpl<WebsiteGroupDao, WebsiteGroup> implements WebsiteGroupService {

    @Resource
    private WebsiteDao webSiteDao;

    @PostConstruct
    private void postConstruct() {
        refreshGroupNameCache();
    }

    @Override
    public List<WebsiteGroup> listAll() {
        QueryWrapper<WebsiteGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1)
                .orderByDesc("create_time");
        return list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> save(WebsiteGroup group, LoginUserVO user) {
        //   暂时不考虑重名
        group.setCreateTime(new Date());
        group.setStatus(1);
        group.setIsDel(false);
        boolean success = save(group);
        if (success) {
            refreshGroupNameCache();
        }
        return success ? Result.ok() : Result.error("保存失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> delete(Integer id, LoginUserVO user) {
        // 检查分组是否存在
        WebsiteGroup group = getById(id);
        if (group == null) {
            return Result.error("分组不存在");
        }

        // 检查分组下是否有网站
        QueryWrapper<Website> websiteQuery = new QueryWrapper<>();
        websiteQuery.eq("group_id", id)
                .eq("status", 1);
        if (webSiteDao.selectCount(websiteQuery) > 0) {
            return Result.error("该分组下存在网站，无法删除");
        }

        // 使用MyBatis-Plus的逻辑删除
        boolean success = removeById(id);
        if (success) {
            refreshGroupNameCache();
        }
        return success ? Result.ok() : Result.error("删除失败");
    }

    @Override
    public void refreshGroupNameCache() {
        // 只缓存启用且未删除的分组
        QueryWrapper<WebsiteGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1)
                .eq("is_del", false);
        List<WebsiteGroup> groups = list(queryWrapper);
        
        // 清空并更新缓存
        GROUP_NAME_CACHE.clear();
        groups.forEach(group -> GROUP_NAME_CACHE.put(group.getId(), group.getGroupName()));
    }

    @Override
    public List<WebsiteGroupVO> listWithWebsites() {
        return baseMapper.listWithWebsites();
    }
} 