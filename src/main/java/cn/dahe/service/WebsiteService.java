package cn.dahe.service;

import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.entity.Website;
import cn.dahe.model.query.WebsiteQuery;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.model.vo.WebsiteVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-08-11
 */
public interface WebsiteService extends IService<Website> {


    PageResult<Website> page(WebsiteQuery query);


    PageResult<Website> pageByWebIds(String webIds,int page,int limit);
    PageResult<Website> pageByWebIds(List<Long> websiteIds,int page,int limit);




    Result<String> save(Website WebSite, LoginUserVO user);


    Result<String> update(Website WebSite, LoginUserVO user);



    Result<String> updateStatus(String id, LoginUserVO user);


    List<Website> listByStatus(int status);

    /**
     * 获取所有站点列表
     *
     * @return 所有站点列表
     */
    List<WebsiteVO> listTotal();

    /**
     * 获取站点的检测策略
     *
     * @param websiteId 站点ID
     * @return 检测策略
     */
    Integer getCheckStrategy(Long websiteId);

    /**
     * 更新网站巡查精准度
     *
     * @param websiteIds 网站ID
     * @param checkStrategy 巡查精准度值
     */
    boolean updateCheckStrategy(List<Long> websiteIds, Integer checkStrategy);
}
