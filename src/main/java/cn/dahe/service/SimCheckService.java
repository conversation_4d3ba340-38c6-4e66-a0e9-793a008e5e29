package cn.dahe.service;

import cn.dahe.model.dto.ArticleCheckDto;
import cn.dahe.entity.CheckWord;
import lombok.Data;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class SimCheckService {

    // 预设错词库
    private static final List<CheckWord> ERROR_WORDS = Arrays.asList(
            // 错别字
            createWord("融入", "融入和", 2, 1L, 52L, null),
            createWord("电子游戏", "电子游戏的", 2, 1L, 52L, 3051L),
            createWord("习近平总书记在河南考察时重要讲话精神", "习近平总书记在河南考察时的重要讲话精神", 1, 2L, 62L, null),
            createWord("公平竞争审查条例", "《公平竞争审查条例》", 1, 2L, 64L, null),
            createWord("数据", "数据等", 2, 1L, 52L, null),
            createWord("和", "和重要", 3, 1L, 52L, null)
    );

    private static CheckWord createWord(String errorWord, String suggestWord, Integer errorLevel,
                                        Long firstErrorTypeId, Long secondErrorTypeId, Long thirdErrorTypeId) {
        CheckWord word = new CheckWord();
        word.setErrorWord(errorWord);
        word.setSuggestWord(suggestWord);
        word.setErrorLevel(errorLevel);
        word.setFirstErrorType(firstErrorTypeId);
        word.setSecondErrorType(secondErrorTypeId);
        word.setThirdErrorType(thirdErrorTypeId);
        return word;
    }

    /**
     * 模拟校对文章
     */
    public List<ArticleCheckDto> simulateCheck(String title, String content) {
        List<ArticleCheckDto> results = new ArrayList<>();

        // 检查标题
        for (CheckWord word : ERROR_WORDS) {
            int index = title.indexOf(word.getErrorWord());
            while (index != -1) {
                ArticleCheckDto check = new ArticleCheckDto();
                check.setErrorWord(word.getErrorWord());
                check.setSuggestWord(word.getSuggestWord());
                check.setErrorLevel(word.getErrorLevel());
                check.setErrorType(word.getThirdErrorType() == null?word.getSecondErrorType(): word.getThirdErrorType());
                check.setArticleLocation(1);
                check.setPosition(index);
                results.add(check);

                // 继续查找下一个位置
                index = title.indexOf(word.getErrorWord(), index + 1);
            }
        }

        // 检查内容
        for (CheckWord word : ERROR_WORDS) {
            int index = content.indexOf(word.getErrorWord());
            while (index != -1) {
                ArticleCheckDto check = new ArticleCheckDto();
                check.setErrorWord(word.getErrorWord());
                check.setSuggestWord(word.getSuggestWord());
                check.setErrorLevel(word.getErrorLevel());
                check.setErrorType(word.getThirdErrorType() == null?word.getSecondErrorType(): word.getThirdErrorType());
                check.setArticleLocation(0);
                check.setPosition(index);
                results.add(check);

                // 继续查找下一个位置
                index = content.indexOf(word.getErrorWord(), index + 1);
            }
        }

        return results;
    }

    public static Date randomDate() {
        // 定义日期范围：2022年1月1日到2024年12月31日
        long startMillis = 1640995200000L;  // 2022-01-01 00:00:00
        long endMillis = 1735689599000L;    // 2024-12-31 23:59:59

        // 生成范围内的随机时间戳
        Random random = new Random();
        long randomTime = startMillis + (long) (random.nextDouble() * (endMillis - startMillis));

        // 根据随机时间戳创建Date对象
        return new Date(randomTime);
    }

    public PushArticle pushRandomArticle() {
        Random random = new Random();
        int index = random.nextInt(SAMPLE_ARTICLES.size());
        //  暂时固定
        // index = 0;
        PushArticle article = SAMPLE_ARTICLES.get(index);
        article.setPubTime(randomDate());
        return article;
    }

    @Data
    public static class PushArticle {
        private Long websiteId;
        private String title;
        /**
         * 这里假设是正文
         */
        private String content;
        private String url;
        private Date pubTime;
    }

    private static final List<PushArticle> SAMPLE_ARTICLES = Arrays.asList(
            createArticle(
                    6L,
                    "<b>河南省召开数字化转型工作推进会</b>",
                    "<p>近日，河南省召开数字化转型工作推进会。会议强调，要深入贯彻习近平总书记在河南考察时重要讲话精神，" +
                            "加快推进数字化转型。要<b>融</b>入数字化发展大局，推动数<i>据</i>共享。要加强电子游戏管理，促进行业健康发展。</p>" +
                            "<p>会议要求，要严格执行公平竞争审查条例，优化营商环境。各部门要加强协作，形成工作合力。</p>",
                    "http://www.example.com/news/1"
            ),
            createArticle(
                    7L,
                    "<p>关于加强和改进新时代档案工作的实施意见</p>",
                    "<p>为深入贯彻落实《中共中央办公厅、国务院办公厅关于加强和改进新时代档案工作的意见》，结合我省实际，" +
                            "制定如下实施意见。</p><p>一、总体要求<br/>坚持以习近平新时代中国特色社会主义思想为指导，全面贯彻党的二十大精神，" +
                            "认真落实习近平总书记关于档案工作的重要指示批示精神，紧紧围绕中心服务大局。</p>" +
                            "<p>二、主要任务<br/>1. 加强顶层设计<br/>2. 完善基础设施<br/>3. 推进数字化建设</p>",
                    "http://www.example.com/news/2"
            ),
            createArticle(
                    7L,
                    "<b>我省开展重点领域专项整治行动</b>",
                    "<p>为进一步规范市场秩序，保障群众利益，我省即日起开展重点领域专项整治行动。" +
                            "本次行动重点整治以下领域：</p>" +
                            "<p>1. 食品数据安全<br/>2. 医疗卫生<br/>3. 教育培训<br/>4. 金融服务</p>" +
                            "<p>各地要高度重视，加强组织领导，细化工作方案，确保整治工作取得实效。</p>",
                    "http://www.example.com/news/3"
            ),
            createArticle(
                    6L,
                    "紧急提醒！游戏诈骗瞄准学生，家长速看！",
                    "<br>他们蹭着电子游戏流量<br><br>假以<br><br>皮肤免费抽、装备大甩卖、账号便宜卖<br><br>等虚假交易的幌子<br><br>通过短视频、游戏等平台<br><br>发布虚假广告<br><br>社交软件私下交易<br><br>得手后<br><br>直接拉黑……",
                    "http://www.example.com/news/5"
            )
    );

    private static PushArticle createArticle(Long websiteId, String title, String content, String url) {
        PushArticle article = new PushArticle();
        article.setWebsiteId(websiteId);
        article.setTitle(title);
        article.setContent(content);
        article.setUrl(url);
        return article;
    }
}
