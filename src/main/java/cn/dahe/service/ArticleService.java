package cn.dahe.service;

import cn.dahe.model.dto.ArticleSaveDto;
import cn.dahe.model.dto.Result;
import cn.dahe.entity.Article;
import cn.dahe.entity.CheckTask;
import cn.dahe.enums.AuditStatusEnum;
import cn.dahe.model.query.ArticleCheckQuery;
import cn.dahe.model.vo.ArticleCheckVO;
import cn.dahe.model.vo.LoginUserVO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * 采集文章Service
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface ArticleService extends BaseService<Article> {


    /**
     * 分页查询文章检查结果
     *
     * @param query 查询参数
     * @return 分页结果
     */
    IPage<ArticleCheckVO> pageArticleAndCheckResults(ArticleCheckQuery query);

    ArticleCheckVO getArticleAndCheckResults(Long articleId, ArticleCheckQuery query);
    /**
     * 更新文章审核状态
     *
     * @param articleIds 文章ID列表，以逗号分隔
     * @param auditStatus 审核状态
     * @return 操作结果
     */
    boolean updateAuditStatus(List<Long> articleIds, AuditStatusEnum auditStatus);

    /**
     * 处置
     * @param articleIds
     * @param disposalStatus
     * @param remark
     * @param loginUserVO
     * @return
     */
    Result<String> updateDisposalStatus(String articleIds, int disposalStatus, String remark, LoginUserVO loginUserVO);


    /**
     * 保存推送的文章
     *
     * @param saveDTO 文章保存参数
     * @return 保存的文章实体
     */
    Article savePushArticle(ArticleSaveDto saveDTO);

    void updateCheckInfo(Long articleId, Long articleContentId);

    void updateArticleCheckRelation(Long articleId, CheckTask checkTask);
}