package cn.dahe.service;

import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.entity.ChkAllSiteSearch;
import cn.dahe.model.query.ChkAllSiteSearchQuery;
import cn.dahe.model.vo.ChkAllSiteSearchVO;
import cn.dahe.model.vo.LoginUserVO;

/**
 * 全站搜索Service
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
public interface ChkAllSiteSearchService extends BaseService<ChkAllSiteSearch> {

    /**
     * 分页查询搜索记录
     *
     * @param query 查询参数
     * @return 分页结果
     */
    PageResult<ChkAllSiteSearchVO> page(ChkAllSiteSearchQuery query);

    /**
     * 从采集中心拉取数据
     *
     * @param user 操作用户
     * @return 拉取结果
     */
    Result<String> pullDataFromCollectionCenter(LoginUserVO user);
}
