package cn.dahe.service;

import cn.dahe.model.dto.PageResult;
import cn.dahe.model.query.UserSiteColumnQuery;
import cn.dahe.model.vo.UserSiteColumnVO;
import com.alibaba.fastjson.JSONObject;

import java.util.List;

/**
 * 用户站点栏目关联Service
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
public interface UserSiteColumnService {

    /**
     * 分页查询用户站点栏目分配
     *
     * @param query 查询参数
     * @return 分页结果
     */
    PageResult<UserSiteColumnVO> page(UserSiteColumnQuery query);

    /**
     * 获取站点列表（从采集中心获取）
     *
     * @param siteName 站点名称关键词
     * @return 站点列表
     */
    JSONObject getSiteList(String siteName);

    /**
     * 获取站点的栏目列表（从采集中心获取）
     *
     * @param siteId 站点ID
     * @return 栏目列表
     */
    JSONObject getSiteColumns(Integer siteId);

    /**
     * 获取用户已分配的站点列表
     *
     * @param userId 用户ID
     * @return 已分配的站点列表
     */
    List<UserSiteColumnVO> getUserAssignedSites(Integer userId);

    /**
     * 获取用户在指定站点下已分配的栏目列表
     *
     * @param userId 用户ID
     * @param siteId 站点ID
     * @return 已分配的栏目列表
     */
    List<UserSiteColumnVO> getUserAssignedColumns(Integer userId, Integer siteId);

    /**
     * 为用户分配站点栏目
     *
     * @param userId 用户ID
     * @param assignments 分配信息列表
     * @param currentUserId 当前操作用户ID
     * @param currentUserName 当前操作用户姓名
     * @return 分配结果
     */
    boolean assignSiteColumns(Integer userId, List<SiteColumnAssignVO> assignments, String currentUserId, String currentUserName);

    /**
     * 删除用户的站点栏目分配
     *
     * @param userId 用户ID
     * @param siteIds 站点ID列表
     * @return 删除结果
     */
    boolean removeUserSiteAssignments(Integer userId, List<Integer> siteIds);

    /**
     * 删除用户的特定站点栏目分配
     *
     * @param userId 用户ID
     * @param siteId 站点ID
     * @param columnIds 栏目ID列表
     * @return 删除结果
     */
    boolean removeUserSiteColumnAssignments(Integer userId, Integer siteId, List<Integer> columnIds);

    /**
     * 站点栏目分配VO
     */
    class SiteColumnAssignVO {
        private Integer siteId;
        private String siteName;
        private String siteUrl;
        private List<ColumnAssignVO> columns;

        // getters and setters
        public Integer getSiteId() { return siteId; }
        public void setSiteId(Integer siteId) { this.siteId = siteId; }
        public String getSiteName() { return siteName; }
        public void setSiteName(String siteName) { this.siteName = siteName; }
        public String getSiteUrl() { return siteUrl; }
        public void setSiteUrl(String siteUrl) { this.siteUrl = siteUrl; }
        public List<ColumnAssignVO> getColumns() { return columns; }
        public void setColumns(List<ColumnAssignVO> columns) { this.columns = columns; }
    }

    /**
     * 栏目分配VO
     */
    class ColumnAssignVO {
        private Integer columnId;
        private String columnName;
        private String columnUrl;

        // getters and setters
        public Integer getColumnId() { return columnId; }
        public void setColumnId(Integer columnId) { this.columnId = columnId; }
        public String getColumnName() { return columnName; }
        public void setColumnName(String columnName) { this.columnName = columnName; }
        public String getColumnUrl() { return columnUrl; }
        public void setColumnUrl(String columnUrl) { this.columnUrl = columnUrl; }
    }
}
