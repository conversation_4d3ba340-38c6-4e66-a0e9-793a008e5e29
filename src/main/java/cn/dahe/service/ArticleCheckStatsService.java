package cn.dahe.service;

import cn.dahe.model.query.ArticleCheckStatQuery;
import cn.dahe.model.query.ArticleCheckWordStatQuery;
import cn.dahe.model.vo.WebsiteArticleCheckStatsVO;
import cn.dahe.model.vo.WebsiteArticleCheckTotalStatsVO;
import cn.dahe.model.vo.WebsiteArticleCheckWordStatsVO;
import cn.dahe.model.vo.WebsiteArticleCheckWordTotalStatsVO;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 网站文章错误统计服务接口
 */
public interface ArticleCheckStatsService {

    
    /**
     * 获取网站错误统计信息列表
     */
    IPage<WebsiteArticleCheckStatsVO> pageStatsByWebsite(ArticleCheckStatQuery query);
    
    /**
     * 获取总体统计概况
     */
    WebsiteArticleCheckTotalStatsVO queryTotalStats(ArticleCheckStatQuery query);

    WebsiteArticleCheckWordTotalStatsVO queryWordTotalStats(ArticleCheckWordStatQuery query);

    IPage<WebsiteArticleCheckWordStatsVO> pageWordStatsByWebsite(ArticleCheckWordStatQuery query);
} 