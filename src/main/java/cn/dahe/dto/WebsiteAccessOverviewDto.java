package cn.dahe.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 网站访问概览DTO
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Data
@Schema(description = "网站访问概览DTO")
public class WebsiteAccessOverviewDto {

    @Schema(description = "序号，从开始递增，分页不间断")
    private Integer id;

    @Schema(description = "网站ID")
    private Integer webId;

    @Schema(description = "网站名称")
    private String webName;

    @Schema(description = "网站地址")
    private String webUrl;

    @Schema(description = "分组名称")
    private String groupName;

    @Schema(description = "总访问次数")
    private Integer totalCount;

    @Schema(description = "成功访问次数")
    private Integer successCount;

    @Schema(description = "失败访问次数")
    private Integer errorCount;

    @Schema(description = "成功率")
    private String successRate;

    @Schema(description = "错误率")
    private String errorRate;

    @Schema(description = "平均响应时间(毫秒)")
    private Double avgResponseTime;

    @Schema(description = "最后检查时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastCheckTime;

    @Schema(description = "状态：0异常，1正常")
    private Integer status;

    @Schema(description = "状态描述")
    public String getStatusDesc() {
        return status != null && status == 1 ? "正常" : "异常";
    }
}