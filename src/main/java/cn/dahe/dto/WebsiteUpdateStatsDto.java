package cn.dahe.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 网站更新统计DTO
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Data
@Schema(description = "网站更新统计DTO")
public class WebsiteUpdateStatsDto {

    @Schema(description = "总站点数")
    private Integer totalSites;

    @Schema(description = "正常站点数")
    private Integer normalSites;

    @Schema(description = "异常站点数")
    private Integer errorSites;

    @Schema(description = "异常率")
    private String errorRate;

    @Schema(description = "检查站点数")
    private Integer checkedSites;

    @Schema(description = "未检查站点数")
    private Integer uncheckedSites;

    @Schema(description = "检查覆盖率")
    private String checkCoverage;

    @Schema(description = "平均响应时间(毫秒)")
    private Double avgResponseTime;

    @Schema(description = "最快响应时间(毫秒)")
    private Double minResponseTime;

    @Schema(description = "最慢响应时间(毫秒)")
    private Double maxResponseTime;
}
