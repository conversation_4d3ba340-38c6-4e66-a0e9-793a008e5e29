package cn.dahe.dao;

import cn.dahe.entity.CheckWord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 文章检查错误词库Dao接口
 */
@Mapper
public interface CheckWordDao extends BaseMapper<CheckWord> {
    
    /**
     * 查询错词唯一记录，目前只根据错词本身
     *
     * @param errorWord 错误词
     * @return 错误词记录
     */
    CheckWord selectDistinctWord(String errorWord);

    /**
     * 批量根据错误词查询记录
     *
     * @param errorWords 错误词列表
     * @return 错误词记录列表
     */
    List<CheckWord> selectBatchDistinctWord(@Param("errorWords") List<String> errorWords);
} 