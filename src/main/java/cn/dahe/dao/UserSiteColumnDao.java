package cn.dahe.dao;

import cn.dahe.entity.UserSiteColumn;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户站点栏目关联DAO
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Mapper
public interface UserSiteColumnDao extends BaseMapper<UserSiteColumn> {

    /**
     * 根据用户ID和站点ID查询栏目列表
     *
     * @param userId 用户ID
     * @param siteId 站点ID
     * @return 栏目列表
     */
    List<UserSiteColumn> selectByUserIdAndSiteId(@Param("userId") Integer userId, @Param("siteId") Integer siteId);

    /**
     * 根据用户ID查询所有分配的站点栏目
     *
     * @param userId 用户ID
     * @return 站点栏目列表
     */
    List<UserSiteColumn> selectByUserId(@Param("userId") Integer userId);

    /**
     * 检查用户、站点、栏目组合是否已存在
     *
     * @param userId 用户ID
     * @param siteId 站点ID
     * @param columnId 栏目ID
     * @return 记录数
     */
    int countByUserSiteColumn(@Param("userId") Integer userId, @Param("siteId") Integer siteId, @Param("columnId") Integer columnId);

    /**
     * 批量删除用户的站点栏目分配
     *
     * @param userId 用户ID
     * @param siteIds 站点ID列表
     * @return 删除记录数
     */
    int deleteByUserIdAndSiteIds(@Param("userId") Integer userId, @Param("siteIds") List<Integer> siteIds);

    /**
     * 批量删除用户的特定站点栏目分配
     *
     * @param userId 用户ID
     * @param siteId 站点ID
     * @param columnIds 栏目ID列表
     * @return 删除记录数
     */
    int deleteByUserIdAndSiteIdAndColumnIds(@Param("userId") Integer userId, @Param("siteId") Integer siteId, @Param("columnIds") List<Integer> columnIds);
}
