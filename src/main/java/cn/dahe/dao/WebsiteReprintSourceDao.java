package cn.dahe.dao;

import cn.dahe.entity.WebsiteReprintSource;
import cn.dahe.model.query.WebsiteReprintSourceQuery;
import cn.dahe.model.vo.WebsiteReprintSourceVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 站点转载信源关联表 Dao接口
 */
@Mapper
public interface WebsiteReprintSourceDao extends BaseMapper<WebsiteReprintSource> {
    
    /**
     * 分页查询转载信源列表
     *
     * @param page 分页参数
     * @param query 查询条件
     * @return 转载信源列表
     */
    Page<WebsiteReprintSourceVO> pageList(Page<WebsiteReprintSourceVO> page, @Param("query") WebsiteReprintSourceQuery query);
}
