package cn.dahe.dao;

import cn.dahe.entity.Article;
import cn.dahe.model.query.ArticleCheckQuery;
import cn.dahe.model.vo.ArticleCheckVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 采集文章Dao
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Mapper
public interface ArticleDao extends BaseMapper<Article> {

    /**
     * 分页查询文章检查结果
     *
     * @param page 分页参数
     * @param query 查询参数
     * @return 分页结果
     */
    IPage<ArticleCheckVO> pageArticleInfo(IPage<ArticleCheckVO> page, @Param("query") ArticleCheckQuery query);

    /**
     * 获取文章的错误列表
     *
     * @param articleId   文章ID
     * @param query 查询条件
     * @return 错误列表
     */
    ArticleCheckVO getArticleInfo(@Param("articleId") Long articleId,
                                  @Param("query") ArticleCheckQuery query);


}