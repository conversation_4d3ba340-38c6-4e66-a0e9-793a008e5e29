package cn.dahe.dao;

import cn.dahe.entity.User;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.dahe.entity.UserRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 */
@Mapper
public interface UserDao extends BaseMapper<User> {


    List<User> listByFilters(@Param("userName") String userName,
                             @Param("roleId") String roleId,
                             @Param("depIdList") List depIdList);


    List<User> listByRoleIdAndUserName(@Param("userName") String userName,
                                       @Param("roleId") String roleId);
}