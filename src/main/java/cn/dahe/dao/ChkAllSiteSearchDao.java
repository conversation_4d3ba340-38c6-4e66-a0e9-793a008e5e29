package cn.dahe.dao;

import cn.dahe.entity.ChkAllSiteSearch;
import cn.dahe.model.query.ChkAllSiteSearchQuery;
import cn.dahe.model.vo.ChkAllSiteSearchVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 全站搜索Dao
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@Mapper
public interface ChkAllSiteSearchDao extends BaseMapper<ChkAllSiteSearch> {

    /**
     * 分页查询搜索记录（包含扩展信息）
     *
     * @param page 分页参数
     * @param query 查询参数
     * @return 分页结果
     */
    IPage<ChkAllSiteSearchVO> selectPageWithExtInfo(
            Page<ChkAllSiteSearchVO> page,
            @Param("query") ChkAllSiteSearchQuery query
    );
}
