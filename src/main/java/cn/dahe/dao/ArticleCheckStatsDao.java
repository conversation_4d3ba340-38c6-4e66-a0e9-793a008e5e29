package cn.dahe.dao;

import cn.dahe.model.query.ArticleCheckStatQuery;
import cn.dahe.model.query.ArticleCheckWordStatQuery;
import cn.dahe.model.vo.WebsiteArticleCheckStatsVO;
import cn.dahe.model.vo.WebsiteArticleCheckTotalStatsVO;
import cn.dahe.model.vo.WebsiteArticleCheckWordStatsVO;
import cn.dahe.model.vo.WebsiteArticleCheckWordTotalStatsVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 网站文章错误统计数据访问层
 */
@Mapper
public interface ArticleCheckStatsDao {

    /**
     * 获取指定时间范围内的网站统计数据
     */
    IPage<WebsiteArticleCheckStatsVO> pageStatsByWebsite(IPage<WebsiteArticleCheckStatsVO> page, @Param("query") ArticleCheckStatQuery query);

    /**
     * 获取指定时间范围内的总体统计数据
     */
    WebsiteArticleCheckTotalStatsVO queryTotalStats(@Param("query") ArticleCheckStatQuery query);

    WebsiteArticleCheckWordTotalStatsVO queryWordTotalStats(@Param("query") ArticleCheckWordStatQuery query);

    IPage<WebsiteArticleCheckWordStatsVO> pageWordStatsByWebsite(Page<WebsiteArticleCheckWordStatsVO> page, @Param("query") ArticleCheckWordStatQuery query);
} 