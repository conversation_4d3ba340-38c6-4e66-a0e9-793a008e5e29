package cn.dahe.dao;

import cn.dahe.entity. WebsiteOutLinkCheckRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 *
 */
@Mapper
public interface WebsiteOutLinkCheckRecordDao extends BaseMapper< WebsiteOutLinkCheckRecord> {


    /**
     * 统计
     *
     * @param webId       网站ID，对应字段 web_id
     * @param sourcePage  来源页面地址，对应字段 source_page
     * @param linkUrl 死链地址，对应字段 dead_link_url
     * @return 满足条件的死链记录数量
     */
    int countByFilters(@Param(value = "webId") String webId,
                       @Param(value = "sourcePage") String sourcePage,
                       @Param(value = "linkUrl") String linkUrl,
                       @Param(value = "httpCode") String httpCode,
                       @Param(value = "beginTime") String beginTime,
                       @Param(value = "endTime") String endTime);
}