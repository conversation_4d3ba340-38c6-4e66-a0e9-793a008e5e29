package cn.dahe.dao;

import cn.dahe.entity.ChkUpdateSiteIndex;
import cn.dahe.model.query.ChkUpdateSiteIndexQuery;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 首页更新检查Dao - 专注于首页更新检查功能
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Mapper
public interface ChkUpdateSiteIndexDao extends BaseMapper<ChkUpdateSiteIndex> {

    // ==================== 首页更新检查概览 ====================
    
    /**
     * 获取首页更新检查概览统计
     *
     * @param query 查询参数
     * @return 统计数据
     */
    Map<String, Object> getOverviewStatistics(@Param("query") ChkUpdateSiteIndexQuery query);

    // ==================== 首页更新检查记录 ====================
    
    /**
     * 分页查询首页更新检查记录（带扩展信息）
     *
     * @param page 分页参数
     * @param groupId 分组ID（逗号分隔的多个值）
     * @param groupName 分组名称（逗号分隔的多个值）
     * @param websiteId 网站ID（逗号分隔的多个值）
     * @param websiteName 网站名称（逗号分隔的多个值）
     * @param websiteIndexUrl 网站首页URL（逗号分隔的多个值）
     * @param beginTime 解析开始时间
     * @param endTime 解析结束时间
     * @return 分页结果
     */
    IPage<Map<String, Object>> selectPageWithExtInfo(
            Page<Map<String, Object>> page,
            @Param("groupId") String groupId,
            @Param("groupName") String groupName,
            @Param("websiteId") String websiteId,
            @Param("websiteName") String websiteName,
            @Param("websiteIndexUrl") String websiteIndexUrl,
            @Param("beginTime") String beginTime,
            @Param("endTime") String endTime
    );

    /**
     * 根据ID获取首页更新检查详情
     *
     * @param id ID
     * @return 详情
     */
    Map<String, Object> selectDetailById(@Param("id") Long id);

    /**
     * 分页查询chk_update_site_index表的详细记录
     *
     * @param page 分页参数
     * @param groupId 分组ID（逗号分隔的多个值）
     * @param groupName 分组名称（逗号分隔的多个值）
     * @param websiteId 网站ID（逗号分隔的多个值）
     * @param websiteName 网站名称（逗号分隔的多个值）
     * @param websiteIndexUrl 网站首页URL（逗号分隔的多个值）
     * @param beginTime 解析开始时间
     * @param endTime 解析结束时间
     * @return 分页结果
     */
    IPage<ChkUpdateSiteIndex> selectDetailPage(
            Page<ChkUpdateSiteIndex> page,
            @Param("groupId") String groupId,
            @Param("groupName") String groupName,
            @Param("websiteId") String websiteId,
            @Param("websiteName") String websiteName,
            @Param("websiteIndexUrl") String websiteIndexUrl,
            @Param("beginTime") String beginTime,
            @Param("endTime") String endTime
    );

}
