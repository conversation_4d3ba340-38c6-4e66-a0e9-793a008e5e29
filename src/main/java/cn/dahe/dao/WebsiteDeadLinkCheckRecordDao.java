package cn.dahe.dao;

import cn.dahe.entity.WebsiteDeadLinkCheckRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 *
 */
@Mapper
public interface WebsiteDeadLinkCheckRecordDao extends BaseMapper<WebsiteDeadLinkCheckRecord> {

    /**
     * 统计指定网站ID、来源页面、死链地址的死链记录数量（按三字段分组）。
     *
     * @param webId       网站ID，对应字段 web_id
     * @param sourcePage  来源页面地址，对应字段 source_page
     * @param linkUrl 死链地址，对应字段 dead_link_url
     * @return 满足条件的死链记录数量（按三字段分组后返回的数量）
     */
    int countGroupedByWebIdAndSourcePageAndDeadLinkUrl(@Param(value = "webId") String webId,
                                                       @Param(value = "sourcePage") String sourcePage,
                                                       @Param(value = "linkUrl") String linkUrl,
                                                       @Param(value = "httpCode") String httpCode,
                                                       @Param(value = "beginTime") String beginTime,
                                                       @Param(value = "endTime") String endTime);


    /**
     * 统计死链记录按网站ID和死链地址分组后的组数。
     * <p>
     * 查询满足指定条件的记录，并按 {@code web_id} 和 {@code dead_link_url} 两个字段分组，
     * 返回分组后的总组数（即满足条件的不同网站+死链地址组合的数量）。
     *
     * @param webId       网站ID（对应字段：web_id）
     * @param sourcePage  来源页面地址（对应字段：source_page）
     * @param linkUrl 死链地址（对应字段：dead_link_url）
     * @param httpCode    HTTP状态码（对应字段：http_code）
     * @param beginTime   检查起始时间（对应字段：check_time >=）
     * @param endTime     检查结束时间（对应字段：check_time <=）
     * @return 分组后的记录数量（组数）
     */
    int countGroupRowsByWebIdAndDeadLinkUrl(@Param(value = "webId") String webId,
                                          @Param(value = "sourcePage") String sourcePage,
                                          @Param(value = "linkUrl") String linkUrl,
                                          @Param(value = "httpCode") String httpCode,
                                          @Param(value = "beginTime") String beginTime,
                                          @Param(value = "endTime") String endTime);

    /**
     * 统计
     *
     * @param webId       网站ID，对应字段 web_id
     * @param sourcePage  来源页面地址，对应字段 source_page
     * @param linkUrl 死链地址，对应字段 dead_link_url
     * @return 满足条件的死链记录数量
     */
    int countByFilters(@Param(value = "webId") String webId,
                       @Param(value = "sourcePage") String sourcePage,
                       @Param(value = "linkUrl") String linkUrl,
                       @Param(value = "httpCode") String httpCode,
                       @Param(value = "beginTime") String beginTime,
                       @Param(value = "endTime") String endTime);
}