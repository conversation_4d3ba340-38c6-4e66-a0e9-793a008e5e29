package cn.dahe.dao;

import cn.dahe.entity.CheckErrorTypeMapping;
import cn.dahe.model.vo.check.CheckErrorTypeMappingVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 错误类型映射Dao
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Mapper
public interface CheckErrorTypeMappingDao extends BaseMapper<CheckErrorTypeMapping> {
    
    /**
     * 获取映射关系列表（包含来源名称和目标类型名称）
     */
    List<CheckErrorTypeMappingVO> getMappingList();
    
    /**
     * 根据来源和错误码获取映射关系
     */
    CheckErrorTypeMappingVO getMappingBySourceError(String sourceCode, String sourceErrorCode);
} 