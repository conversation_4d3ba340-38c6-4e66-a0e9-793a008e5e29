package cn.dahe.dao;

import cn.dahe.dto.WebsiteAccessOverviewDto;
import cn.dahe.entity.WebsiteAccessRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 *
 */
@Mapper
public interface WebsiteAccessRecordDao extends BaseMapper<WebsiteAccessRecord> {


    List<WebsiteAccessOverviewDto> listByFilters(@Param(value = "webIdList") List<String> webIdList,
                                                 @Param(value = "groupType") String groupType,
                                                 @Param(value = "beginTime") String beginTime,
                                                 @Param(value = "endTime") String endTime);

}