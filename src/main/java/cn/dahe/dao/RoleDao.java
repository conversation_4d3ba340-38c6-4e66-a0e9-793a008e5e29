package cn.dahe.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.dahe.entity.Role;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 *
 */
@Mapper
public interface RoleDao extends BaseMapper<Role> {


    /**
     *
     */
    List<Role> listByFilters(@Param(value = "roleIds") List<String> roleIds,
                             @Param(value = "roleSns") List<String> roleSns,
                             @Param(value = "permissionStatus") String permissionStatus
    );

}