package cn.dahe.dao;

import cn.dahe.entity.Attachment;
import cn.dahe.model.query.AttachmentCheckQuery;
import cn.dahe.model.vo.AttachmentCheckVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 附件检查Dao - 对应原型图功能
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Mapper
public interface AttachmentDao extends BaseMapper<Attachment> {

    // ==================== 附件检查记录 ====================

    /**
     * 分页查询附件检查记录（包含扩展信息）
     *
     * @param page 分页参数
     * @param query 查询参数
     * @return 分页结果
     */
    IPage<AttachmentCheckVO> pageAttachmentInfo(
            Page<AttachmentCheckVO> page,
            @Param("query") AttachmentCheckQuery query
    );

    /**
     * 根据ID获取附件检查详情
     *
     * @param id ID
     * @return 详情
     */
    AttachmentCheckVO getAttachmentInfo(@Param("id") Long id);

}
