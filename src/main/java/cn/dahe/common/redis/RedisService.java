package cn.dahe.common.redis;


import cn.dahe.utils.StringUtils;
import org.springframework.data.redis.core.BoundSetOperations;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Component
public class RedisService {

    @Resource
    public RedisTemplate redisTemplate;
    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key   缓存的键值
     * @param value 缓存的值
     */
    public <T> void setCacheObject(final String key, final T value) {
        if (StringUtils.isBlank(key)) {
            return;
        }
        redisTemplate.opsForValue().set(key, value);
    }

    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key      缓存的键值
     * @param value    缓存的值
     * @param timeout  时间
     * @param timeUnit 时间颗粒度
     */
    public <T> void setCacheObject(final String key, final T value, final Long timeout, final TimeUnit timeUnit) {
        if (StringUtils.isBlank(key)) {
            return;
        }
        redisTemplate.opsForValue().set(key, value, timeout, timeUnit);
    }

    /**
     * 设置有效时间
     *
     * @param key     Redis键
     * @param timeout 超时时间
     * @return true=设置成功；false=设置失败
     */
    public boolean expireKey(final String key, final long timeout) {
        if (StringUtils.isBlank(key)) {
            return false;
        }
        return expire(key, timeout, TimeUnit.SECONDS);
    }

    /**
     * 设置有效时间
     *
     * @param key     Redis键
     * @param timeout 超时时间
     * @param unit    时间单位
     * @return true=设置成功；false=设置失败
     */
    public boolean expire(final String key, final long timeout, final TimeUnit unit) {
        if (StringUtils.isBlank(key)) {
            return false;
        }
        return redisTemplate.expire(key, timeout, unit);
    }

    /**
     * 获取有效时间
     *
     * @param key Redis键
     * @return 有效时间
     */
    public long getExpire(final String key) {
        if (StringUtils.isBlank(key)) {
            return 0;
        }
        return redisTemplate.getExpire(key);
    }

    /**
     * 判断 key是否存在
     *
     * @param key 键
     * @return true 存在 false不存在
     */
    public Boolean hasKey(String key) {
        if (StringUtils.isBlank(key)) {
            return false;
        }
        return redisTemplate.hasKey(key);
    }

    /**
     * 获得缓存的基本对象。
     *
     * @param key 缓存键值
     * @return 缓存键值对应的数据
     */
    public <T> T getCacheObject(final String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        ValueOperations<String, T> operation = redisTemplate.opsForValue();
        return operation.get(key);
    }

    public Object getCacheObject2(final String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        return redisTemplate.opsForValue().get(key);
    }

    /**
     * 删除单个对象
     *
     * @param key
     */
    public boolean deleteObject(final String key) {
        if (StringUtils.isBlank(key)) {
            return false;
        }
        return redisTemplate.delete(key);
    }

    /**
     * 根据前缀删除所有key
     *
     * @param pattern 前缀
     */
    public void deleteKeys(String pattern) {
        if (StringUtils.isBlank(pattern)) {
            return;
        }
        Collection<String> keys = this.keys(pattern + "*");
        if (keys != null && !keys.isEmpty()) {
            redisTemplate.delete(keys);
        }
    }

    /**
     * 删除集合对象
     *
     * @param collection 多个对象
     * @return
     */
    public boolean deleteObject(final Collection collection) {
        if (collection == null || collection.isEmpty()) {
            return false;
        }
        return redisTemplate.delete(collection) > 0;
    }

    /**
     * 缓存List数据
     *
     * @param key      缓存的键值
     * @param dataList 待缓存的List数据
     * @return 缓存的对象
     */
    public <T> long setCacheList(final String key, final List<T> dataList) {
        if (StringUtils.isBlank(key)) {
            return 0;
        }
        Long count = redisTemplate.opsForList().rightPushAll(key, dataList);
        return count == null ? 0 : count;
    }

    /**
     * 缓存List数据
     *
     * @param key      缓存的键值
     * @param dataList 待缓存的List数据
     * @return 缓存的对象
     */
    public <T> long lpush(final String key, String value) {
        if (StringUtils.isBlank(key)) {
            return 0;
        }
        Long count = redisTemplate.opsForList().leftPush(key, value);
        return count == null ? 0 : count;
    }


    public <T> long lRem(final String key, long count, String value) {
        if (StringUtils.isBlank(key)) {
            return 0;
        }
        return redisTemplate.opsForList().remove(key, count, value) == null ? 0 : count;
    }


    public void lTrim(final String key, long start, long end) {
        redisTemplate.opsForList().trim(key, start, end);
    }


    /**
     * 缓存List数据
     *
     * @param key      缓存的键值
     * @param dataList 待缓存的List数据
     * @return 缓存的对象
     */
    public <T> String rpop(final String key) {
        String value = "";
        if (StringUtils.isBlank(key)) {
            return value;
        }
        return redisTemplate.opsForList().rightPop(key).toString();
    }

    /**
     * 获得缓存的list对象
     *
     * @param key 缓存的键值
     * @return 缓存键值对应的数据
     */
    public <T> List<T> getCacheList(final String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        return redisTemplate.opsForList().range(key, 0, -1);
    }

    public int llen(final String key) {
        if (StringUtils.isBlank(key)) {
            return 0;
        }
        return redisTemplate.opsForList().size(key).intValue();
    }

    /**
     * 缓存Set
     *
     * @param key     缓存键值
     * @param dataSet 缓存的数据
     * @return 缓存数据的对象
     */
    public <T> BoundSetOperations<String, T> setCacheSet(final String key, final Set<T> dataSet) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        BoundSetOperations<String, T> setOperation = redisTemplate.boundSetOps(key);
        Iterator<T> it = dataSet.iterator();
        while (it.hasNext()) {
            setOperation.add(it.next());
        }
        return setOperation;
    }

    /**
     * 获得缓存的set
     *
     * @param key
     * @return
     */
    public <T> Set<T> getCacheSet(final String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        return redisTemplate.opsForSet().members(key);
    }

    /**
     * 缓存Map
     *
     * @param key
     * @param dataMap
     */
    public <T> void setCacheMap(final String key, final Map<String, T> dataMap) {
        if (StringUtils.isBlank(key)) {
            return;
        }
        if (dataMap != null) {
            redisTemplate.opsForHash().putAll(key, dataMap);
        }
    }

    /**
     * 获得缓存的Map
     *
     * @param key
     * @return
     */
    public <T> Map<String, T> getCacheMap(final String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        return redisTemplate.opsForHash().entries(key);
    }

    /**
     * 往Hash中存入数据
     *
     * @param key   Redis键
     * @param hKey  Hash键
     * @param value 值
     */
    public <T> void setCacheMapValue(final String key, final String hKey, final T value) {
        if (StringUtils.isBlank(key)) {
            return;
        }
        redisTemplate.opsForHash().put(key, hKey, value);
    }

    /**
     * 获取Hash中的数据
     *
     * @param key  Redis键
     * @param hKey Hash键
     * @return Hash中的对象
     */
    public <T> T getCacheMapValue(final String key, final String hKey) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        HashOperations<String, String, T> opsForHash = redisTemplate.opsForHash();
        return opsForHash.get(key, hKey);
    }

    /**
     * 获取多个Hash中的数据
     *
     * @param key   Redis键
     * @param hKeys Hash键集合
     * @return Hash对象集合
     */
    public <T> List<T> getMultiCacheMapValue(final String key, final Collection<Object> hKeys) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        return redisTemplate.opsForHash().multiGet(key, hKeys);
    }

    /**
     * 删除Hash中的某条数据
     *
     * @param key  Redis键
     * @param hKey Hash键
     * @return 是否成功
     */
    public boolean deleteCacheMapValue(final String key, final String hKey) {
        if (StringUtils.isBlank(key)) {
            return false;
        }
        return redisTemplate.opsForHash().delete(key, hKey) > 0;
    }

    public void incr(String redisKey) {
        if (StringUtils.isBlank(redisKey)) {
            return;
        }
        redisTemplate.opsForValue().increment(redisKey);
    }

    public void decr(String redisKey) {
        if (StringUtils.isBlank(redisKey)) {
            return;
        }
        redisTemplate.opsForValue().decrement(redisKey);
    }

    /**
     * 删除Hash中的某条数据
     *
     * @param hKey Hash键
     * @return 是否成功
     */
    public void convertAndSend(final String hKey) {
        if (StringUtils.isBlank(hKey)) {
            return;
        }
        redisTemplate.convertAndSend("LOGIN_TAG", hKey);
    }

    /**
     * 获得缓存的基本对象列表
     *
     * @param pattern 字符串前缀
     * @return 对象列表
     */
    public Collection<String> keys(final String pattern) {
        if (StringUtils.isBlank(pattern)) {
            return null;
        }
        return redisTemplate.keys(pattern);

    }


}
