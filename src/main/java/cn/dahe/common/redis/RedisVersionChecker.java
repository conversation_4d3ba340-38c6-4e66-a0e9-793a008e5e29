package cn.dahe.common.redis;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Slf4j
@Component
public class RedisVersionChecker {

    @Autowired
    private RedisConnectionFactory connectionFactory;

    @PostConstruct
    public void checkRedisVersion() {
        try {
            String info = connectionFactory.getConnection().info().get("redis_version").toString();
            log.info("Redis 版本: {}", info);
            
            String[] version = info.split("\\.");
            int majorVersion = Integer.parseInt(version[0]);
            
            if (majorVersion < 5) {
                log.error("当前 Redis 版本 {} 不支持 Stream 特性，需要 Redis 5.0 或更高版本", info);
                throw new RuntimeException("Redis 版本不支持 Stream 特性");
            } else {
                log.info("Redis 版本检查通过，支持 Stream 特性");
            }
        } catch (Exception e) {
            log.error("检查 Redis 版本失败", e);
            throw new RuntimeException("检查 Redis 版本失败", e);
        }
    }
}
