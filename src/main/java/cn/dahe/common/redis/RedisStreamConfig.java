package cn.dahe.common.redis;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.stream.ObjectRecord;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.data.redis.stream.StreamMessageListenerContainer;


import java.time.Duration;

/**
 * Redis Stream 配置类
 * <p>
 * 该配置类主要用于设置 Redis Stream 消息队列的相关配置：
 * 1. 创建专用的 RedisTemplate，用于处理 Stream 操作
 * 2. 配置消息监听容器的选项（超时、目标类型等）
 * 3. 创建消息监听容器，用于消费消息
 */
@Slf4j
@Configuration
public class RedisStreamConfig {



    /**
     * 文章处理队列名称
     */
    public static final String ARTICLE_STREAM_KEY = "article:stream";

    /**
     * 消费组名称
     */
    public static final String ARTICLE_CONSUMER_GROUP = "article:consumer:group";

    /**
     * 消费者名称前缀
     */
    public static final String CONSUMER_PREFIX = "consumer:";


    /**
     * 创建专用于 Stream 操作的 RedisTemplate
     * 
     * @param connectionFactory Redis 连接工厂
     * @return 配置好的 RedisTemplate 实例
     * <p>
     * 说明：
     * 1. 使用 StringRedisSerializer 处理 key，避免乱码
     * 2. 使用 GenericJackson2JsonRedisSerializer 处理 value，支持复杂对象的序列化
     * 3. name = "streamRedisTemplate" 指定 Bean 名称，方便其他组件注入时区分
     */
    @Bean(name = "streamRedisTemplate")
    public RedisTemplate<String, Object> streamRedisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        // 使用 StringRedisSerializer 来序列化和反序列化 redis 的 key
        template.setKeySerializer(new StringRedisSerializer());
        // 使用 GenericJackson2JsonRedisSerializer 来序列化和反序列化 redis 的 value
        template.setValueSerializer(new GenericJackson2JsonRedisSerializer());

        // Hash 的 key 也采用 StringRedisSerializer 的序列化方式
        template.setHashKeySerializer(new StringRedisSerializer());
        // Hash 的 value 也采用 GenericJackson2JsonRedisSerializer 的序列化方式
        template.setHashValueSerializer(new GenericJackson2JsonRedisSerializer());

        template.afterPropertiesSet();
        return template;
    }

    /**
     * 配置 Stream 消息监听容器的选项
     * 
     * @return 消息监听容器的配置选项
     * <p>
     * 说明：
     * 1. pollTimeout：拉取消息的超时时间，这里设置为1秒
     * 2. targetType：目标类型，这里使用 String，因为我们的消息内容是 JSON 字符串
     * 3. 这些选项会被传递给消息监听容器，影响其行为
     */
    @Bean
    public StreamMessageListenerContainer.StreamMessageListenerContainerOptions<String, ObjectRecord<String, String>> streamMessageListenerContainerOptions() {
        return StreamMessageListenerContainer
                .StreamMessageListenerContainerOptions
                .builder()
                .pollTimeout(Duration.ofSeconds(1))
                .targetType(String.class)
                .build();
    }

    /**
     * 创建 Stream 消息监听容器
     * 
     * @param connectionFactory Redis 连接工厂
     * @param streamMessageListenerContainerOptions 监听容器的配置选项
     * @return 配置好的消息监听容器
     * 
     * 说明：
     * 1. 消息监听容器用于自动监听和消费 Stream 中的消息
     * 2. 容器会根据配置的选项定期轮询 Stream
     * 3. 当收到消息时，会调用注册的消息处理器进行处理
     * 4. 支持消费组模式，可以实现消息的可靠投递和负载均衡
     */
    @Bean
    public StreamMessageListenerContainer<String, ObjectRecord<String, String>> streamMessageListenerContainer(
            RedisConnectionFactory connectionFactory,
            StreamMessageListenerContainer.StreamMessageListenerContainerOptions<String, ObjectRecord<String, String>> streamMessageListenerContainerOptions) {
        return StreamMessageListenerContainer.create(connectionFactory, streamMessageListenerContainerOptions);
    }
}
