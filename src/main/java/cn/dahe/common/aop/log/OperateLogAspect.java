package cn.dahe.common.aop.log;

import cn.dahe.common.auth.SecurityUtils;
import cn.dahe.common.auth.ServletUtils;
import cn.dahe.model.dto.Result;
import cn.dahe.enums.OperateTypeEnum;
import cn.dahe.service.OperateLogService;
import cn.dahe.utils.JsonUtils;
import cn.dahe.utils.StringUtils;
import cn.dahe.model.vo.LoginUserVO;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.annotation.Annotation;
import java.lang.reflect.Array;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Predicate;
import java.util.stream.IntStream;

import static org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR;

@Aspect
@Component
public class OperateLogAspect {
    private static final Logger log = LoggerFactory.getLogger(OperateLogAspect.class);

    @Resource
    private OperateLogService operateLogService;


    /**
     * 用于记录操作内容的上下文
     */
    private static final ThreadLocal<String> CONTENT = new ThreadLocal<>();
    /**
     * 用于记录拓展字段的上下文
     */
    private static final ThreadLocal<Map<String, Object>> EXTS = new ThreadLocal<>();

    @Around("@annotation(operateLog)")
    public Object around(ProceedingJoinPoint joinPoint, OperateLog operateLog) throws Throwable {
        log.info("===========Around===========");
        return around0(joinPoint, operateLog);
    }

    private Object around0(ProceedingJoinPoint joinPoint,
                           OperateLog operateLog) throws Throwable {
        // 记录开始时间
        LocalDateTime startTime = LocalDateTime.now();
        try {
            // 执行原有方法
            Object result = joinPoint.proceed();
            // 记录正常执行时的操作日志
            this.log(joinPoint, operateLog, startTime, result, null);
            return result;
        } catch (Throwable exception) {
            this.log(joinPoint, operateLog, startTime, null, exception);
            throw exception;
        } finally {
            clearThreadLocal();
        }
    }

    public static void setContent(String content) {
        CONTENT.set(content);
    }

    public static void addExt(String key, Object value) {
        if (EXTS.get() == null) {
            EXTS.set(new HashMap<>());
        }
        EXTS.get().put(key, value);
    }

    private static void clearThreadLocal() {
        CONTENT.remove();
        EXTS.remove();
    }

    private void log(ProceedingJoinPoint joinPoint,
                     OperateLog operateLog,
                     LocalDateTime startTime, Object result, Throwable exception) {
        try {
            // 判断不记录的情况
            if (!isLogEnable(joinPoint, operateLog)) {
                return;
            }
            // 真正记录操作日志
            this.log0(joinPoint, operateLog, startTime, result, exception);
        } catch (Throwable ex) {
            log.error("[log][记录操作日志时，发生异常，其中参数是 joinPoint({}) operateLog({}) apiOperation({}) result({}) exception({}) ]",
                    joinPoint, operateLog, result, exception, ex);
        }
    }

    private void log0(ProceedingJoinPoint joinPoint,
                      OperateLog operateLog,
                      LocalDateTime startTime, Object result, Throwable exception) {
        OperateLogData operateLogData = new OperateLogData();
        // 补全通用字段
        operateLogData.setStartTime(startTime);
        // 补全模块信息
        fillModuleFields(operateLogData, joinPoint, operateLog);
        // 补全请求信息
        fillRequestFields(operateLogData);
        // 补全方法信息
        fillMethodFields(operateLogData, joinPoint, operateLog, startTime, result, exception);
        // 补充用户信息
        fillUserFields(operateLogData);
//        log.info("日志 用户编号 userId：{}", operateLogData.getUserId());
//        log.info("日志 用户类型 userName：{}", operateLogData.getUserName());
//        log.info("日志 操作模块 module：{}", operateLogData.getModule());
//        log.info("日志 操作名 name：{}", operateLogData.getMethodName());
//        log.info("日志 操作分类 type：{}", operateLogData.getType());
//        log.info("日志 操作明细 content：{}", operateLogData.getResultData());
//        log.info("日志 字段 exts：{}", operateLogData.getExts());
//        log.info("日志 请求方法名 requestMethod：{}", operateLogData.getRequestMethod());
//        log.info("日志 请求地址 requestUrl：{}", operateLogData.getRequestUrl());
//        log.info("日志 用户 IP userIp ：{}", operateLogData.getUserIp());
//        log.info("日志 UserAgent：{}", operateLogData.getUserAgent());
//        log.info("日志 javaMethod：{}", operateLogData.getJavaMethod());
//        log.info("日志 方法的参数 javaMethodArgs：{}", operateLogData.getJavaMethodArgs());
//        log.info("日志 开始时间 startTime：{}", operateLogData.getStartTime());
//        log.info("日志 执行时长 duration：{}", operateLogData.getDuration());
//        log.info("日志 结果码 resultCode：{}", operateLogData.getResultCode());
//        log.info("日志 结果数据 resultData：{}", operateLogData.getResultData());
        // 数据库保存日志
        cn.dahe.entity.OperateLog operateLog1 = new cn.dahe.entity.OperateLog();
        BeanUtils.copyProperties(operateLogData, operateLog1);
        operateLogService.save(operateLog1);

    }


    private static void fillUserFields(OperateLogData operateLogData) {
        if ("互联互通对外接口".equals(StringUtils.defaultString(operateLogData.getModule(), ""))) {
            operateLogData.setUserId(0);
            String psiteId = "";
            try {
                psiteId = StringUtils.defaultString(JSONObject.parseObject(operateLogData.getJavaMethodArgs()).getJSONObject("common").getString("psiteId"), "");
            } catch (Exception e) {
                psiteId = "";
                e.printStackTrace();

            }
            operateLogData.setUserName(psiteId);
        } else {
            LoginUserVO loginUser = SecurityUtils.getLoginUser();
            if (loginUser != null) {
                operateLogData.setUserId(loginUser.getUserId());
                operateLogData.setUserName(loginUser.getUsername());
            }
        }
    }

    private static void fillModuleFields(OperateLogData operateLogData,
                                         ProceedingJoinPoint joinPoint,
                                         OperateLog operateLog) {
        // module 属性
        if (operateLog != null) {
            operateLogData.setModule(operateLog.module());
        }
        if (StrUtil.isEmpty(operateLogData.getModule())) {
            //1.先读取方法上的@Api。
            Operation methodApi = getMethodAnnotation(joinPoint, Operation.class);
            Tag classApi = getClassAnnotation(joinPoint, Tag.class);
            String moduleStr = "";
            if (methodApi != null) {
                // 优先读取 @API 的 name 属性
                if (StrUtil.isNotEmpty(methodApi.summary())) {
                    moduleStr = methodApi.summary();
                }
                // 没有的话，读取 @API 的 tags 属性
                if (StrUtil.isEmpty(moduleStr) && ArrayUtil.isNotEmpty(methodApi.tags())) {
                    moduleStr = methodApi.tags()[0];
                }
            }
            if (StrUtil.isEmpty(moduleStr)) {
                if (classApi != null) {
                    // 优先读取 @API 的 name 属性
                    if (StrUtil.isNotEmpty(classApi.name())) {
                        moduleStr = classApi.name();
                    }
                    // 没有的话，读取 @API 的 tags 属性
                    if (StrUtil.isEmpty(moduleStr) && ArrayUtil.isNotEmpty(classApi.description())) {
                        moduleStr = classApi.description();
                    }
                }
            }
            operateLogData.setModule(moduleStr);
        }
        // name 属性
        if (operateLog != null) {
            operateLogData.setMethodName(operateLog.name());
        }
        operateLogData.setMethodName(operateLogData.getMethodName());
        // type 属性
        if (operateLog != null && ArrayUtil.isNotEmpty(operateLog.type())) {
            operateLogData.setType(operateLog.type()[0].getType());
        }
        if (operateLogData.getType() == null) {
            RequestMethod requestMethod = obtainFirstMatchRequestMethod(obtainRequestMethod(joinPoint));
            OperateTypeEnum operateLogType = convertOperateLogType(requestMethod);
            operateLogData.setType(operateLogType != null ? operateLogType.getType() : null);
        }
        // content 和 exts 属性
        operateLogData.setContent(CONTENT.get());
        operateLogData.setExts(EXTS.get());
    }

    private static void fillRequestFields(OperateLogData operateLogData) {
        // 获得 Request 对象
        HttpServletRequest request = ServletUtils.getRequest();
        if (request == null) {
            return;
        }
        // 补全请求信息
        operateLogData.setRequestMethod(request.getMethod());
        operateLogData.setRequestUrl(request.getRequestURI());
        operateLogData.setUserIp(ServletUtil.getClientIP(request));
        operateLogData.setUserAgent(ServletUtils.getUserAgent(request));
    }

    private static void fillMethodFields(OperateLogData operateLogData,
                                         ProceedingJoinPoint joinPoint,
                                         OperateLog operateLog,
                                         LocalDateTime startTime, Object result, Throwable exception) {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        operateLogData.setJavaMethod(methodSignature.toString());
        if (operateLog == null || operateLog.logArgs()) {
            operateLogData.setJavaMethodArgs(obtainMethodArgs(joinPoint));
        }
        if (operateLog == null || operateLog.logResultData()) {
            operateLogData.setResultData(obtainResultData(result));
        }
        operateLogData.setDuration((int) (LocalDateTimeUtil.between(startTime, LocalDateTime.now()).toMillis()));
        // （正常）处理 resultCode 和 resultMsg 字段
        if (result instanceof Result) {
            Result<?> commonResult = (Result<?>) result;
            operateLogData.setResultCode(commonResult.getCode());
            operateLogData.setResultMsg(commonResult.getMsg());
        } else {
            operateLogData.setResultCode(HttpStatus.OK.value());
        }
        // （异常）处理 resultCode 和 resultMsg 字段
        if (exception != null) {
            operateLogData.setResultCode(INTERNAL_SERVER_ERROR.value());
            operateLogData.setResultMsg(ExceptionUtil.getRootCauseMessage(exception));
        }
    }

    private static boolean isLogEnable(ProceedingJoinPoint joinPoint,
                                       OperateLog operateLog) {
        // 有 @OperateLog 注解的情况下
        if (operateLog != null) {
            return operateLog.enable();
        }
        // 没有 @ApiOperation 注解的情况下，只记录 POST、PUT、DELETE 的情况
        return obtainFirstLogRequestMethod(obtainRequestMethod(joinPoint)) != null;
    }

    private static RequestMethod obtainFirstLogRequestMethod(RequestMethod[] requestMethods) {
        if (ArrayUtil.isEmpty(requestMethods)) {
            return null;
        }
        return Arrays.stream(requestMethods).filter(requestMethod ->
                requestMethod == RequestMethod.POST
                        || requestMethod == RequestMethod.PUT
                        || requestMethod == RequestMethod.DELETE)
                .findFirst().orElse(null);
    }

    private static RequestMethod obtainFirstMatchRequestMethod(RequestMethod[] requestMethods) {
        if (ArrayUtil.isEmpty(requestMethods)) {
            return null;
        }
        // 优先，匹配最优的 POST、PUT、DELETE
        RequestMethod result = obtainFirstLogRequestMethod(requestMethods);
        if (result != null) {
            return result;
        }
        // 然后，匹配次优的 GET
        result = Arrays.stream(requestMethods).filter(requestMethod -> requestMethod == RequestMethod.GET)
                .findFirst().orElse(null);
        if (result != null) {
            return result;
        }
        // 兜底，获得第一个
        return requestMethods[0];
    }

    private static OperateTypeEnum convertOperateLogType(RequestMethod requestMethod) {
        if (requestMethod == null) {
            return null;
        }
        switch (requestMethod) {
            case GET:
                return OperateTypeEnum.GET;
            case POST:
                return OperateTypeEnum.CREATE;
            case PUT:
                return OperateTypeEnum.UPDATE;
            case DELETE:
                return OperateTypeEnum.DELETE;
            default:
                return OperateTypeEnum.OTHER;
        }
    }

    private static RequestMethod[] obtainRequestMethod(ProceedingJoinPoint joinPoint) {
        RequestMapping requestMapping = AnnotationUtils.getAnnotation( // 使用 Spring 的工具类，可以处理 @RequestMapping 别名注解
                ((MethodSignature) joinPoint.getSignature()).getMethod(), RequestMapping.class);
        return requestMapping != null ? requestMapping.method() : new RequestMethod[]{};
    }

    @SuppressWarnings("SameParameterValue")
    private static <T extends Annotation> T getMethodAnnotation(ProceedingJoinPoint joinPoint, Class<T> annotationClass) {
        return ((MethodSignature) joinPoint.getSignature()).getMethod().getAnnotation(annotationClass);
    }

    @SuppressWarnings("SameParameterValue")
    private static <T extends Annotation> T getClassAnnotation(ProceedingJoinPoint joinPoint, Class<T> annotationClass) {
        return (T) joinPoint.getSignature().getDeclaringType().getAnnotation(annotationClass);
    }

    private static String obtainMethodArgs(ProceedingJoinPoint joinPoint) {
        // TODO 提升：参数脱敏和忽略
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        String[] argNames = methodSignature.getParameterNames();
        Object[] argValues = joinPoint.getArgs();
        // 拼接参数
        Map<String, Object> args = Maps.newHashMapWithExpectedSize(argValues.length);
        for (int i = 0; i < argNames.length; i++) {
            String argName = argNames[i];
            Object argValue = argValues[i];
            // 被忽略时，标记为 ignore 字符串，避免和 null 混在一起
            args.put(argName, argValue != null && !isIgnoreArgs(argValue) ? argValue : "[ignore]");
        }
        return JsonUtils.toJsonString(args);
    }

    private static String obtainResultData(Object result) {
        // TODO 提升：结果脱敏和忽略
        if (result instanceof Result) {
            result = ((Result<?>) result).getData();
        }
        return JsonUtils.toJsonString(result);
    }

    private static boolean isIgnoreArgs(Object object) {
        Class<?> clazz = object.getClass();
        // 处理数组的情况
        if (clazz.isArray()) {
            return IntStream.range(0, Array.getLength(object))
                    .anyMatch(index -> isIgnoreArgs(Array.get(object, index)));
        }
        // 递归，处理数组、Collection、Map 的情况
        if (Collection.class.isAssignableFrom(clazz)) {
            return ((Collection<?>) object).stream()
                    .anyMatch((Predicate<Object>) OperateLogAspect::isIgnoreArgs);
        }
        if (Map.class.isAssignableFrom(clazz)) {
            return isIgnoreArgs(((Map<?, ?>) object).values());
        }
        // obj
        return object instanceof MultipartFile
                || object instanceof HttpServletRequest
                || object instanceof HttpServletResponse
                || object instanceof BindingResult;
    }
}
