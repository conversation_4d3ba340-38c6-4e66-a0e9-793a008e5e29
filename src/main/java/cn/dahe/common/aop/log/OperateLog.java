package cn.dahe.common.aop.log;



import cn.dahe.enums.OperateTypeEnum;

import java.lang.annotation.*;


/**
 * <AUTHOR>
 * //@OperateLog(logArgs = false) // 上传文件，没有记录操作日志的必要
 * // @OperateLog(enable = false) // 避免 Post 请求被记录操作日志
 * //    @OperateLog(enable = false) // 回调地址，无需记录操作日志
 */
@SuppressWarnings("checkstyle:WhitespaceAfter")
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.CONSTRUCTOR, ElementType.PARAMETER, ElementType.TYPE_USE})
//作用于方法
@Retention(RetentionPolicy.RUNTIME) //运行时有效
@Documented
public @interface OperateLog {


    /**
     * 操作模块.
     */
    String module() default "";

    /**
     * 操作名
     */
    String name() default "";

    /**
     * 操作分类
     * <p>
     * 实际并不是数组，因为枚举不能设置 null 作为默认值
     */
    OperateTypeEnum[] type() default {};

    // ========== 开关字段 ==========

    /**
     * 是否记录操作日志
     */
    boolean enable() default true;

    /**
     * 是否记录方法参数
     */
    boolean logArgs() default true;

    /**
     * 是否记录方法结果的数据
     */
    boolean logResultData() default false;

}
