package cn.dahe.common.advice;


import cn.dahe.common.model.ResultCode;
import cn.dahe.model.dto.Result;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.TypeMismatchException;
import org.springframework.http.HttpStatus;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.validation.BindException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.stream.Collectors;


/**
 * 捕获全局异常
 */
@Slf4j
@RestControllerAdvice
public class GlobalException {

//    @Resource
//    private CasCustomerConfig casCustomerConfig;

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result<String> methodArgumentNotValidHandler(MethodArgumentNotValidException ex) {
        String message = ex.getBindingResult().getFieldErrors().stream()
                .map(fieldError -> StrUtil.format("{}：{}：{}", fieldError.getField(), fieldError.getRejectedValue(), fieldError.getDefaultMessage()))
                .collect(Collectors.joining(";"));
        return Result.error(ResultCode.ParamSetIllegal, message);
    }

    /**
     * 处理参数校验异常
     */
    @ExceptionHandler(BindException.class)
    @ResponseBody
    public Result<String> handleValidationException(BindException ex) {
        String message = ex.getBindingResult().getFieldErrors().stream()
                .map(fieldError -> StrUtil.format("{}：{}：{}", fieldError.getField(), fieldError.getRejectedValue(), fieldError.getDefaultMessage()))
                .collect(Collectors.joining(";"));
        return Result.error(ResultCode.ValidateError, message);
    }

    /**
     * 请求参数不全
     *
     * @param ex 异常
     * @return 返回值
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public Result<String> missingServletRequestParameterException(MissingServletRequestParameterException ex) {
        log.warn("请求参数不全：【{}】", ex.getMessage());
        return Result.error(ResultCode.ParamSetIllegal);
    }

    @ExceptionHandler(TypeMismatchException.class)
    public Result<String> typeMismatchException(TypeMismatchException ex) {
        log.warn("请求参数类型不正确：【{}】", ex.getMessage());
        return Result.error(ResultCode.ParamSetIllegal);
    }

    /**
     * 未知异常捕获
     *
     * @param e 异常
     * @return 结果
     */
    @ExceptionHandler(Exception.class)
    @ResponseBody
    public Result<String> handleException(Exception e) {
        log.info("GlobalException-->Exception【{}】", e.getMessage());
        e.printStackTrace();
        return Result.error(ResultCode.INTERNAL_SERVER_ERROR);
    }


    @ExceptionHandler(BadSqlGrammarException.class)
    public Result<String> handleBadSqlGrammarException(BadSqlGrammarException e) {
        log.info("GlobalException-->BadSqlGrammarException报错信息【{}】", e.getMessage());
        e.printStackTrace();
        return Result.error(ResultCode.SystemError);
    }

    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    @ExceptionHandler(NotPermissionException.class)
    @ResponseBody
    public Result<String> handleNotPermissionException(NotPermissionException e) {
        log.info("GlobalException-->NotPermissionException【{}】", e.getMessage());
        e.printStackTrace();
        return Result.error(ResultCode.NotGrant, e.getMessage());
    }

    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    @ExceptionHandler(NotRoleException.class)
    @ResponseBody
    public Result<String> handleNotRoleException(NotRoleException e) {
        log.info("GlobalException-->NotRoleException【{}】", e.getMessage());
        e.printStackTrace();
        return Result.error(ResultCode.NotGrant, e.getMessage());
    }

    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseBody
    public Result<String> handleMethodNotSupportedException(HttpRequestMethodNotSupportedException e) {
        log.info("GlobalException-->handleMethodNotSupportedException【{}】", e.getMessage());
        e.printStackTrace();
        return Result.error(ResultCode.METHOD_NOT_ALLOWED);
    }


    @ExceptionHandler(NotLoginException.class)
    @ResponseBody
    public Result<Object> handleNotLoginException(NotLoginException e) {
        log.info("GlobalException-->NotLoginException【{}】", e.getMessage());
//        e.printStackTrace();
        return Result.error(ResultCode.NoLoginError, e.getMessage(), null);
    }


}
