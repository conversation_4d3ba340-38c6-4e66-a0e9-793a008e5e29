package cn.dahe.common.constants;

import cn.hutool.core.lang.hash.Hash;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 角色相关
 *
 * <AUTHOR>
 * @date 2023-07-05
 */
public class RoleConstants {


    /**
     * 超级管理员
     */
    public static final String ADMIN_SUPER = "CJGLY";

    /**
     * 小管理员
     */
    public static final String ADMIN_SMALL = "SMGLY";

    /**
     * 填报人
     */
    public static final String REPORT = "TBR";


    /**
     * 初评专家
     */
    public static final String EXPERT_CHUPING = "CPZJ";

    /**
     * 定评专家
     */
    public static final String EXPERT_DINGPING = "DPZJ";

    /**
     * 管理员的角色
     */
    public static Set<String> adminRoles = new HashSet<>(Arrays.asList(ADMIN_SUPER, ADMIN_SMALL));

    public static Set<String> adminRoles_and_expert = new HashSet<>(Arrays.asList(ADMIN_SUPER, ADMIN_SMALL, EXPERT_CHUPING, EXPERT_DINGPING));


    /**
     * 初评专家
     */
    public static final String EXPERT_CHUPING_ID = "12";

    /**
     * 定评专家
     */
    public static final String EXPERT_DINGPING_ID = "13";


}
