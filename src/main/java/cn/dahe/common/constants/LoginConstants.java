package cn.dahe.common.constants;

/**
 * @author:<EMAIL>
 * @createDate:2023-03-09-14:30
 * @description:
 */
public class LoginConstants {
    public static final String LOGIN_FROM_PC = "pc";
    public static final String LOGIN_FROM_MOBILE = "mobile";
    public static final String LOGIN_IN_ACTIVE = "dev,local";
    public static final String LOGIN_TEST_CODE = "6666";
    public static final String LOGIN_SUCCESS = "登录成功";
    public static final String LOGIN_FAIL = "登录失败";
    public static final String LOGOUT_SUCCESS = "退出成功";
    public static final String LOGOUT_FAIL = "退出失败";
    public static final String LOGIN_FAIL_ILLEGAL = "非法登录，请检查后重试";
    public static final String LOGIN_FAIL_USER_NOT_EXIST = "用户不存在，请检查后重试";
    public static final String LOGIN_FAIL_PHONE_ERROR = "手机号格式不正确，请检查后重试";
    public static final String LOGIN_FAIL_ACCOUNT_LOCKED = "账号已被锁定，请联系管理员";
    public static final String LOGIN_FAIL_ACCOUNT_EXPIRED = "账号已过期，请联系管理员";
    public static final String LOGIN_FAIL_ACCOUNT_DISABLED = "账号已被禁用，请联系管理员";
    public static final String LOGIN_FAIL_SMS_NOT_SEND = "验证码未发送，请点击获取验证码";
    public static final String LOGIN_FAIL_SMS_NOT_EXIST = "验证码不存在，请核对输入是否正确";

    public static final String LOGIN_FAIL_PASSPORT_ERROR = "密码输入错误，请核对输入是否正确";
    public static final String LOGIN_FAIL_IMG_CODE_NOT_EXIST= "请重新获取图形验证码";
    public static final String LOGIN_FAIL_IMG_CODE_ERROR= "图形验证码输入错误，请核对输入是否正确";
    public static final String LOGIN_FAIL_DEP_ACCOUNT_DISABLED = "所在单位已被禁用，请联系";
    public static final String LOGIN_FAIL_LAPSE = "登录状态已失效，请重新登录";
    public static final String LOGIN_FAIL_OTHER_LOGIN = "您的账号其他设备已登录，请重新登录";
    public static final String LOGIN_FAIL_EQUIPMENT = "请使用浏览器设备进行登录";

    public static final String LOGIN_FAIL_TOKEN_MISSING = "未提供有效的令牌值";
    public static final String LOGIN_FAIL_TOKEN_PARSE_ERROR = "令牌值解析错误";
    public static final String LOGIN_FAIL_USER_NOT_FOUND_OR_DISABLED = "用户不存在或账户已被禁用";
    public static final String LOGIN_FAIL_CACHE_TOKEN_MISMATCH = "缓存中的令牌值不一致";
    public static final String LOGIN_FAIL_USER_NOT_IN_CACHE = "未在缓存中找到该用户";
}
