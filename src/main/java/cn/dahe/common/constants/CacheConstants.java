package cn.dahe.common.constants;

/**
 * * <AUTHOR>
 * * @date 2023-06-29
 */
public class CacheConstants {

    /**
     * 缓存有效期，默认720（分钟）
     */
    public final static long EXPIRATION = 720;

    public final static long EXPIRATION_MOBILE = 30;

    /**
     * 缓存刷新时间，默认120（分钟）
     */
    public final static long REFRESH_TIME = 120;

    /**
     * 密码最大错误次数
     */
    public final static int PASSWORD_MAX_RETRY_COUNT = 5;

    /**
     * 密码锁定时间，默认10（分钟）
     */
    public final static long PASSWORD_LOCK_TIME = 10;


    public static final String SMS_CODE_LOGIN_PHONE = "hlht:sms_code:login:phone_";

    public final static String LOGIN_TOKEN_KEY = "hlht:login_tokens:";

    public final static String LOGIN_USER_KEY = "hlht:login_user:";

    public final static String COLON = ":";
    public final static String COMMA = ",";

//**************************SSO 相关****************************
    /**
     * SSO的token
     */
    public final static String SSO_COM_TOKEN = "sso:COM_TOKEN";

    public final static String SSO_HDSMS_CODE = "gd:ssoqdsms_code";


    /**
     * SSO用户是否登录
     */
    public final static String SSO_USER_LOGINED = "sso:user:logined";


    /**
     * 用户验证码
     */
    public static final String KEY_USER_CHECK_CODE="user:check_code:";


}
