package cn.dahe.common.annotation;

import java.lang.annotation.*;

/**
 * API认证注解
 * 用于标识需要API密钥认证的接口
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ApiAuth {
    
    /**
     * API认证类型
     */
    String value() default "default";
    
    /**
     * 是否必须认证
     */
    boolean required() default true;
    
    /**
     * 认证描述
     */
    String description() default "需要API密钥认证";
}
