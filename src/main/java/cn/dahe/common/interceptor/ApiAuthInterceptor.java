package cn.dahe.common.interceptor;

import cn.dahe.common.annotation.ApiAuth;
import cn.dahe.model.dto.Result;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.security.MessageDigest;
import java.util.Map;
import java.util.TreeMap;

/**
 * API认证拦截器
 * 用于验证外部API调用的安全性
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
@Component
public class ApiAuthInterceptor implements HandlerInterceptor {

    @Value("${api.auth.secret:dahe_collection_center_2025}")
    private String apiSecret;

    @Value("${api.auth.timeout:300000}")
    private long timeoutMs; // 5分钟超时

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 只处理有@ApiAuth注解的方法
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        ApiAuth apiAuth = handlerMethod.getMethodAnnotation(ApiAuth.class);
        
        if (apiAuth == null) {
            return true;
        }

        log.info("API认证拦截器处理请求: {}", request.getRequestURI());

        try {
            // 验证API认证
            if (!validateApiAuth(request)) {
                writeErrorResponse(response, "API认证失败");
                return false;
            }

            log.info("API认证成功: {}", request.getRequestURI());
            return true;
        } catch (Exception e) {
            log.error("API认证异常", e);
            writeErrorResponse(response, "API认证异常：" + e.getMessage());
            return false;
        }
    }

    /**
     * 验证API认证
     */
    private boolean validateApiAuth(HttpServletRequest request) {
        // 获取认证参数
        String apiKey = request.getHeader("X-API-Key");
        String timestamp = request.getHeader("X-Timestamp");
        String signature = request.getHeader("X-Signature");
        String nonce = request.getHeader("X-Nonce");

        log.info("API认证参数 - apiKey: {}, timestamp: {}, nonce: {}, signature: {}", 
                apiKey, timestamp, nonce, signature);

        // 检查必要参数
        if (!StringUtils.hasText(apiKey) || !StringUtils.hasText(timestamp) || 
            !StringUtils.hasText(signature) || !StringUtils.hasText(nonce)) {
            log.warn("API认证参数不完整");
            return false;
        }

        // 验证API Key（这里可以从数据库或配置中获取有效的API Key列表）
        if (!"collection_center_api_key".equals(apiKey)) {
            log.warn("无效的API Key: {}", apiKey);
            return false;
        }

        // 验证时间戳（防止重放攻击）
        try {
            long requestTime = Long.parseLong(timestamp);
            long currentTime = System.currentTimeMillis();
            if (Math.abs(currentTime - requestTime) > timeoutMs) {
                log.warn("请求时间戳过期，请求时间: {}, 当前时间: {}, 差值: {}ms", 
                        requestTime, currentTime, Math.abs(currentTime - requestTime));
                return false;
            }
        } catch (NumberFormatException e) {
            log.warn("无效的时间戳格式: {}", timestamp);
            return false;
        }

        // 验证签名
        String expectedSignature = generateSignature(apiKey, timestamp, nonce, request);
        if (!signature.equals(expectedSignature)) {
            log.warn("签名验证失败，期望: {}, 实际: {}", expectedSignature, signature);
            return false;
        }

        return true;
    }

    /**
     * 生成签名
     */
    private String generateSignature(String apiKey, String timestamp, String nonce, HttpServletRequest request) {
        try {
            // 构建签名字符串：method + uri + apiKey + timestamp + nonce + secret
            StringBuilder signStr = new StringBuilder();
            signStr.append(request.getMethod().toUpperCase())
                   .append(request.getRequestURI())
                   .append(apiKey)
                   .append(timestamp)
                   .append(nonce)
                   .append(apiSecret);

            // 添加查询参数（如果有）
            Map<String, String> params = new TreeMap<>();
            if (request.getQueryString() != null) {
                String[] pairs = request.getQueryString().split("&");
                for (String pair : pairs) {
                    String[] keyValue = pair.split("=", 2);
                    if (keyValue.length == 2) {
                        params.put(keyValue[0], keyValue[1]);
                    }
                }
            }
            
            for (Map.Entry<String, String> entry : params.entrySet()) {
                signStr.append(entry.getKey()).append(entry.getValue());
            }

            // MD5加密
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(signStr.toString().getBytes("UTF-8"));
            
            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            return hexString.toString().toUpperCase();
        } catch (Exception e) {
            log.error("生成签名失败", e);
            return "";
        }
    }

    /**
     * 写入错误响应
     */
    private void writeErrorResponse(HttpServletResponse response, String message) throws Exception {
        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        
        Result<String> result = Result.error(401, message);
        
        PrintWriter out = response.getWriter();
        out.write(JSON.toJSONString(result));
        out.flush();
    }
}
