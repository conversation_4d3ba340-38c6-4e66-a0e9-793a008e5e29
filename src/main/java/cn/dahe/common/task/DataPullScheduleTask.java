package cn.dahe.common.task;

import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.service.ChkAllSiteSearchService;
import cn.dahe.service.ChkUpdateSiteColumnService;
import cn.dahe.service.ChkUpdateSiteIndexService;
import cn.dahe.utils.FanCollectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

/**
 * 数据拉取定时任务
 * 定时从采集中心拉取各类检查数据
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "schedule.data-pull.enabled", havingValue = "true", matchIfMissing = false)
public class DataPullScheduleTask {

    @Resource
    private ChkAllSiteSearchService chkAllSiteSearchService;

    @Resource
    private ChkUpdateSiteColumnService chkUpdateSiteColumnService;

    @Resource
    private ChkUpdateSiteIndexService chkUpdateSiteIndexService;

    @Value("${schedule.data-pull.all-site-search.enabled:true}")
    private boolean allSiteSearchEnabled;

    @Value("${schedule.data-pull.attach-url.enabled:true}")
    private boolean attachUrlEnabled;

    @Value("${schedule.data-pull.site-column.enabled:true}")
    private boolean siteColumnEnabled;

    @Value("${schedule.data-pull.site-index.enabled:true}")
    private boolean siteIndexEnabled;

    @Value("${schedule.data-pull.connectivity-check.enabled:true}")
    private boolean connectivityCheckEnabled;

    @Value("${schedule.data-pull.dead-links.enabled:true}")
    private boolean deadLinksEnabled;

    @Value("${schedule.data-pull.external-links.enabled:true}")
    private boolean externalLinksEnabled;

    /**
     * 拉取全站搜索数据
     * 每小时执行一次
     */
    @Scheduled(cron = "${schedule.data-pull.all-site-search.cron:0 10 22 * * ?}")
    public void pullAllSiteSearchData(LoginUserVO user) {
        if (!allSiteSearchEnabled) {
            log.debug("全站搜索数据拉取已禁用");
            return;
        }

        log.info("开始执行全站搜索数据拉取定时任务");
        try {

        } catch (Exception e) {
            log.error("全站搜索数据拉取定时任务执行失败", e);
        }
    }

    /**
     * 拉取网站连通性检查数据
     * 每2小时执行一次
     */
    @Scheduled(cron = "${schedule.data-pull.attach-url.cron:0 20 22 * * ?}")
    public void pullConnectivityCheckData(LoginUserVO user) {
        if (!connectivityCheckEnabled) {
            log.debug("网站连通性检查数据拉取已禁用");
            return;
        }

        log.info("开始执行网站连通性检查数据拉取定时任务");
        try {
            // 获取需要检查的网站URL列表（可以从数据库获取）
            List<String> urls = getWebsiteUrlsForConnectivityCheck();

            // 调用FanCollectUtil进行连通性检查
            JSONObject result = FanCollectUtil.checkConnectivity(urls, 10, "GET");

            if (result.getBoolean("success")) {
                // 处理返回的数据并持久化
                processConnectivityCheckData(result);
                log.info("网站连通性检查数据拉取定时任务执行成功");
            } else {
                log.error("网站连通性检查数据拉取定时任务执行失败: {}", result.getString("message"));
            }
        } catch (Exception e) {
            log.error("网站连通性检查数据拉取定时任务执行失败", e);
        }
    }

    /**
     * 拉取死链检查数据
     * 每3小时执行一次
     */
    @Scheduled(cron = "${schedule.data-pull.site-column.cron:0 30 22 * * ?}")
    public void pullDeadLinksData(LoginUserVO user) {
        if (!deadLinksEnabled) {
            log.debug("死链检查数据拉取已禁用");
            return;
        }

        log.info("开始执行死链检查数据拉取定时任务");
        try {
            // 获取需要检查的站点ID列表
            List<Integer> siteIds = getSiteIdsForDeadLinksCheck();

            // 设置时间范围（最近7天）
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusDays(7);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'");

            // 调用FanCollectUtil获取死链数据
            JSONObject result = FanCollectUtil.getDeadLinks(
                siteIds,
                1,
                100,
                startTime.format(formatter),
                endTime.format(formatter)
            );

            if (result.getBoolean("success")) {
                // 处理返回的数据并持久化
                processDeadLinksData(result);
                log.info("死链检查数据拉取定时任务执行成功");
            } else {
                log.error("死链检查数据拉取定时任务执行失败: {}", result.getString("message"));
            }
        } catch (Exception e) {
            log.error("死链检查数据拉取定时任务执行失败", e);
        }
    }

    /**
     * 拉取首页更新检查数据
     * 每4小时执行一次
     */
    @Scheduled(cron = "${schedule.data-pull.site-index.cron:0 40 22 * * ?}")
    public void pullSiteIndexData(LoginUserVO user) {
        if (!siteIndexEnabled) {
            log.debug("首页更新检查数据拉取已禁用");
            return;
        }

        log.info("开始执行首页更新检查数据拉取定时任务");
        try {
            // 获取需要检查的网站列表
            List<String> urls = getWebsiteUrlsForHomepageCheck();

            // 调用FanCollectUtil获取首页更新情况
            JSONObject result = FanCollectUtil.getHomepageUpdates(urls);

            if (result.getBoolean("success")) {
                // 处理返回的数据并持久化
                processHomepageUpdatesData(result);
                log.info("首页更新检查数据拉取定时任务执行成功");
            } else {
                log.error("首页更新检查数据拉取定时任务执行失败: {}", result.getString("message"));
            }
        } catch (Exception e) {
            log.error("首页更新检查数据拉取定时任务执行失败", e);
        }
    }

    /**
     * 拉取异常外链检查数据
     * 每6小时执行一次
     */
    @Scheduled(cron = "0 0 */6 * * ?")
    public void pullExternalLinksData() {
        if (!externalLinksEnabled) {
            log.debug("异常外链检查数据拉取已禁用");
            return;
        }

        log.info("开始执行异常外链检查数据拉取定时任务");
        try {
            // 获取需要检查的站点ID列表
            List<Integer> siteIds = getSiteIdsForExternalLinksCheck();

            // 设置时间范围（最近7天）
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusDays(7);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'");

            // 调用FanCollectUtil获取异常外链数据
            JSONObject result = FanCollectUtil.getExternalLinks(
                siteIds,
                1,
                100,
                startTime.format(formatter),
                endTime.format(formatter)
            );

            if (result.getBoolean("success")) {
                // 处理返回的数据并持久化
                processExternalLinksData(result);
                log.info("异常外链检查数据拉取定时任务执行成功");
            } else {
                log.error("异常外链检查数据拉取定时任务执行失败: {}", result.getString("message"));
            }
        } catch (Exception e) {
            log.error("异常外链检查数据拉取定时任务执行失败", e);
        }
    }

    /**
     * 综合数据拉取任务
     * 每天凌晨2点执行一次，拉取所有类型的数据
     */
    @Scheduled(cron = "${schedule.data-pull.comprehensive.cron:0 50 22 * * ?}")
    public void pullAllData(LoginUserVO user) {
        log.info("开始执行综合数据拉取定时任务");
        
        try {
            // 按顺序拉取各类数据
            if (allSiteSearchEnabled) {
                log.info("拉取全站搜索数据...");
                pullAllSiteSearchData(user);
                Thread.sleep(5000); // 间隔5秒
            }

            if (connectivityCheckEnabled) {
                log.info("拉取网站连通性检查数据...");
                pullConnectivityCheckData(user);
                Thread.sleep(5000); // 间隔5秒
            }

            if (deadLinksEnabled) {
                log.info("拉取死链检查数据...");
                pullDeadLinksData(user);
                Thread.sleep(5000); // 间隔5秒
            }

            if (externalLinksEnabled) {
                log.info("拉取异常外链检查数据...");
                pullExternalLinksData();
                Thread.sleep(5000); // 间隔5秒
            }

            if (siteIndexEnabled) {
                log.info("拉取首页更新检查数据...");
                pullSiteIndexData(user);
            }

            log.info("综合数据拉取定时任务执行完成");
        } catch (Exception e) {
            log.error("综合数据拉取定时任务执行失败", e);
        }
    }

    /**
     * 手动触发全部数据拉取
     * 提供给管理员手动调用
     */
    public void manualPullAllData(LoginUserVO user) {
        log.info("手动触发全部数据拉取");
        pullAllData(user);
    }

    /**
     * 获取定时任务状态
     */
    public String getTaskStatus() {
        StringBuilder status = new StringBuilder();
        status.append("数据拉取定时任务状态:\n");
        status.append("- 全站搜索数据拉取: ").append(allSiteSearchEnabled ? "启用" : "禁用").append("\n");
        status.append("- 网站连通性检查数据拉取: ").append(connectivityCheckEnabled ? "启用" : "禁用").append("\n");
        status.append("- 死链检查数据拉取: ").append(deadLinksEnabled ? "启用" : "禁用").append("\n");
        status.append("- 异常外链检查数据拉取: ").append(externalLinksEnabled ? "启用" : "禁用").append("\n");
        status.append("- 首页更新检查数据拉取: ").append(siteIndexEnabled ? "启用" : "禁用").append("\n");
        return status.toString();
    }

    // ==================== 辅助方法 ====================

    /**
     * 获取需要进行连通性检查的网站URL列表
     */
    private List<String> getWebsiteUrlsForConnectivityCheck() {
        // TODO: 从数据库获取网站URL列表
        // 这里返回示例数据
        return Arrays.asList(
            "https://www.henan.gov.cn",
            "https://www.hnnu.edu.cn",
            "https://www.zut.edu.cn",
            "https://www.hpu.edu.cn"
        );
    }

    /**
     * 获取需要进行首页更新检查的网站URL列表
     */
    private List<String> getWebsiteUrlsForHomepageCheck() {
        // TODO: 从数据库获取网站URL列表
        // 这里返回示例数据
        return Arrays.asList(
            "https://www.henan.gov.cn",
            "https://www.hnnu.edu.cn",
            "https://www.zut.edu.cn"
        );
    }

    /**
     * 获取需要进行死链检查的站点ID列表
     */
    private List<Integer> getSiteIdsForDeadLinksCheck() {
        // TODO: 从数据库获取站点ID列表
        // 这里返回示例数据
        return Arrays.asList(102, 103, 104);
    }

    /**
     * 获取需要进行异常外链检查的站点ID列表
     */
    private List<Integer> getSiteIdsForExternalLinksCheck() {
        // TODO: 从数据库获取站点ID列表
        // 这里返回示例数据
        return Arrays.asList(102, 103, 104);
    }

    // ==================== 数据处理方法 ====================

    /**
     * 处理连通性检查数据并持久化
     */
    private void processConnectivityCheckData(JSONObject result) {
        try {
            JSONArray dataArray = result.getJSONArray("data");
            if (dataArray != null) {
                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject item = dataArray.getJSONObject(i);

                    // TODO: 将连通性检查结果保存到数据库
                    // 可以创建一个新的表来存储连通性检查结果
                    String url = item.getString("url");
                    Integer status = item.getInteger("status");
                    Integer latencyMs = item.getInteger("latency_ms");
                    String timestamp = item.getString("timestamp");
                    JSONObject error = item.getJSONObject("error");

                    log.debug("连通性检查结果: URL={}, Status={}, Latency={}ms", url, status, latencyMs);

                    // TODO: 保存数据
                }
            }

            // 处理元数据
            JSONObject metadata = result.getJSONObject("metadata");
            if (metadata != null) {
                log.info("连通性检查统计: 总URL数={}, 成功检查数={}, 失败检查数={}",
                    metadata.getInteger("total_urls"),
                    metadata.getInteger("successful_checks"),
                    metadata.getInteger("failed_checks"));
            }
        } catch (Exception e) {
            log.error("处理连通性检查数据失败", e);
        }
    }

    /**
     * 处理首页更新数据并持久化
     */
    private void processHomepageUpdatesData(JSONObject result) {
        try {
            JSONArray dataArray = result.getJSONArray("data");
            if (dataArray != null) {
                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject item = dataArray.getJSONObject(i);

                    Integer siteId = item.getInteger("site_id");
                    String url = item.getString("url");
                    String status = item.getString("status"); // "updated" or "unchanged"
                    String lastCheck = item.getString("last_check");
                    Integer latencyMs = item.getInteger("latency_ms");
                    JSONObject error = item.getJSONObject("error");

                    log.debug("首页更新检查结果: SiteId={}, URL={}, Status={}", siteId, url, status);

                    // TODO: 保存数据
                }
            }

            // 处理元数据
            JSONObject metadata = result.getJSONObject("metadata");
            if (metadata != null) {
                log.info("首页更新检查统计: 总数={}, 已更新={}, 未更新={}, 错误={}",
                    metadata.getInteger("total"),
                    metadata.getInteger("updated"),
                    metadata.getInteger("unchanged"),
                    metadata.getInteger("errors"));
            }
        } catch (Exception e) {
            log.error("处理首页更新数据失败", e);
        }
    }

    /**
     * 处理死链数据并持久化
     */
    private void processDeadLinksData(JSONObject result) {
        try {
            JSONObject data = result.getJSONObject("data");
            if (data != null) {
                JSONArray brokenLinks = data.getJSONArray("broken_links");
                if (brokenLinks != null) {
                    for (int i = 0; i < brokenLinks.size(); i++) {
                        JSONObject item = brokenLinks.getJSONObject(i);

                        Integer siteId = item.getInteger("site_id");
                        String linkUrl = item.getString("link_url");
                        String linkName = item.getString("link_name");
                        String contentType = item.getString("content_type");
                        String linkType = item.getString("link_type");
                        String parentUrl = item.getString("parent_url");
                        Integer statusCode = item.getInteger("status_code");
                        String parentPubTime = item.getString("parent_pub_time");
                        String parentWebCode = item.getString("parent_web_code");
                        String collectTime = item.getString("collect_time");

                        log.debug("死链检查结果: SiteId={}, LinkUrl={}, StatusCode={}", siteId, linkUrl, statusCode);

                        // TODO: 保存数据
                    }
                }

                // 处理分页信息
                JSONObject pagination = data.getJSONObject("pagination");
                if (pagination != null) {
                    log.info("死链检查分页信息: 总数={}, 当前页={}, 每页数量={}, 总页数={}",
                        pagination.getInteger("total"),
                        pagination.getInteger("page"),
                        pagination.getInteger("per_page"),
                        pagination.getInteger("total_pages"));
                }
            }
        } catch (Exception e) {
            log.error("处理死链数据失败", e);
        }
    }

    /**
     * 处理异常外链数据并持久化
     */
    private void processExternalLinksData(JSONObject result) {
        try {
            JSONObject data = result.getJSONObject("data");
            if (data != null) {
                JSONArray externalLinks = data.getJSONArray("external_links");
                if (externalLinks != null) {
                    for (int i = 0; i < externalLinks.size(); i++) {
                        JSONObject item = externalLinks.getJSONObject(i);

                        Integer siteId = item.getInteger("site_id");
                        String linkUrl = item.getString("link_url");
                        String linkName = item.getString("link_name");
                        String contentType = item.getString("content_type");
                        String linkType = item.getString("link_type"); // "normal" or "file"
                        String parentUrl = item.getString("parent_url");
                        Integer statusCode = item.getInteger("status_code");
                        String webCode = item.getString("web_code");
                        String collectTime = item.getString("collect_time");

                        log.debug("异常外链检查结果: SiteId={}, LinkUrl={}, StatusCode={}", siteId, linkUrl, statusCode);

                        // TODO: 保存数据
                    }
                }

                // 处理分页信息
                JSONObject pagination = data.getJSONObject("pagination");
                if (pagination != null) {
                    log.info("异常外链检查分页信息: 总数={}, 当前页={}, 每页数量={}, 总页数={}",
                        pagination.getInteger("total"),
                        pagination.getInteger("page"),
                        pagination.getInteger("per_page"),
                        pagination.getInteger("total_pages"));
                }
            }
        } catch (Exception e) {
            log.error("处理异常外链数据失败", e);
        }
    }

}
