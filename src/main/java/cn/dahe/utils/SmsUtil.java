package cn.dahe.utils;


import cn.dahe.common.constants.CacheConstants;
import cn.dahe.model.dto.Result;
import cn.hutool.core.codec.Base64;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.data.redis.core.StringRedisTemplate;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Description : 短信
 * Date : 2019/10/18 9:43
 *
 * <AUTHOR> fy
 */

@Slf4j
public class SmsUtil {

        private static final String smsApi = "https://service.henan.gov.cn/api/sendsms";
//    private static final String smsApi = "https://service.henan.gov.cn/api/common/sms";

    public static Result sendSmsAccount(String phoneNum) throws NoSuchAlgorithmException {
        StringRedisTemplate stringRedisTemplate = SpringUtils.getBean(StringRedisTemplate.class);
        String encodePhone = Base64.encode(phoneNum);
        String token = stringRedisTemplate.opsForValue().get(CacheConstants.SSO_COM_TOKEN);
        String s = stringRedisTemplate.opsForValue().get(CacheConstants.SSO_HDSMS_CODE + encodePhone);
        if (StringUtils.isNotBlank(s)) {
            log.info("已发送的验证码信息：{}", s);
            return Result.error("请勿重复获取，上一个验证码10分钟内有效，请继续使用");
        }
        token = token + new SimpleDateFormat("yyyyMMdd").format(new Date());
        StringBuilder md5HexBuilder = new StringBuilder();
        for (byte b : MessageDigest.getInstance("MD5").digest(token.getBytes())) {
            md5HexBuilder.append(String.format("%02x", b));
        }
        Map<String, String> map = new HashMap<>(2);
        map.put("phone", phoneNum);
        map.put("type", "3");
        Map<String, String> headers = new HashMap<>(1);
        headers.put("token", md5HexBuilder.toString().toUpperCase());
        log.info("短信网址：{}", smsApi);
        log.info("参数：{}", map);
        log.info("token:{}", md5HexBuilder.toString().toUpperCase());
        Response response = OkHttpUtils.newsTimeOutInstance(60).doPostHeader(smsApi, map, headers);
        if (response == null || response.code() != 200) {
            try {
                log.info("返回参数：{}", response.body().string());
            } catch (IOException e) {
                e.printStackTrace();
            }
            return Result.error("请求失败，请联系技术人员！");
        }
        try {
            log.info("返回参数：{}", response.body().string());
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Result.ok();
    }
}
