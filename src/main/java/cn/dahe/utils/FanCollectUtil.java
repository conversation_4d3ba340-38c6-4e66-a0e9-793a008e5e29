package cn.dahe.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * 采集工具类
 * 提供与采集中心的接口调用功能
 */
public class FanCollectUtil {

    /**
     * 测试模式开关
     * true: 返回文档中的示例响应JSON
     * false: 实际调用采集中心API
     */
    private static final boolean TEST_MODE = false;

    /**
     * 采集中心API KEY
     * 鉴权（已完成）
     * 目前暂时以手动分配API KEY的方式鉴权，在header里配置字段：
     * X-DaheCollection-Key
     */
    private static final String API_KEY = "dh_9f3e0617356cda1ee2cba96717d27f586a580596f5eb7ad77075724fe101c344";

    /**
     * 采集中心基础URL
     */
    private static final String BASE_URL = "http://192.168.77.212";

    private static final RestTemplate restTemplate = new RestTemplate();

    /**
     * 对指定链接列表做连通性检查（已完成）
     * POST /site/connectivity
     *
     * @param urls    要检查的URL列表
     * @param timeout 超时时间（秒），可选，默认5秒
     * @param method  请求方法，可选，默认GET，可以是HEAD/GET
     * @return 连通性检查结果
     */
    public static JSONObject checkConnectivity(List<String> urls, Integer timeout, String method) {
        if (TEST_MODE) {
            return getConnectivityTestResponse();
        }

        try {
            JSONObject request = new JSONObject();
            request.put("urls", urls);
            if (timeout != null) {
                request.put("timeout", timeout);
            }
            if (method != null) {
                request.put("method", method);
            }

            return callApi("/site/connectivity", request);
        } catch (Exception e) {
            return createErrorResponse("连通性检查失败", e.getMessage());
        }
    }

    /**
     * 对指定链接列表做连通性检查（简化版本）
     *
     * @param urls 要检查的URL列表
     * @return 连通性检查结果
     */
    public static JSONObject checkConnectivity(List<String> urls) {
        return checkConnectivity(urls, null, null);
    }

    /**
     * 获取网站首页更新情况（已完成）
     * POST /site/homepage-updates
     *
     * @param sites 网站列表，每个网站包含site_id（可选）和url（必填）
     * @return 首页更新情况结果
     */
    public static JSONObject getHomepageUpdates(JSONArray sites) {
        if (TEST_MODE) {
            return getHomepageUpdatesTestResponse();
        }

        try {
            JSONObject request = new JSONObject();
            request.put("sites", sites);

            return callApi("/site/homepage-updates", request);
        } catch (Exception e) {
            return createErrorResponse("获取首页更新情况失败", e.getMessage());
        }
    }

    /**
     * 获取网站首页更新情况（简化版本）
     *
     * @param urls URL列表
     * @return 首页更新情况结果
     */
    public static JSONObject getHomepageUpdates(List<String> urls) {
        try {
            JSONArray sites = new JSONArray();
            for (String url : urls) {
                JSONObject site = new JSONObject();
                site.put("url", url);
                sites.add(site);
            }
            return getHomepageUpdates(sites);
        } catch (Exception e) {
            return createErrorResponse("获取首页更新情况失败", e.getMessage());
        }
    }

    /**
     * 按关键词查询站点（已完成）
     * POST /site/query
     * 根据name查询t_site表获取所有匹配的site_id和name映射
     * {"success":true,"query":"河南理工大学","site_id":{"6309":"河南理工大学"}}
     * @param name 站点名称关键词
     * @return 匹配的站点列表
     */
    public static JSONObject searchSitesByName(String name) {
        if (TEST_MODE) {
            return getSearchSitesTestResponse(name);
        }

        try {
            JSONObject request = new JSONObject();
            request.put("name", name);

            return callApi("/site/query", request);
        } catch (Exception e) {
            return createErrorResponse("查询站点失败", e.getMessage());
        }
    }

    /**
     * 获取网站和栏目列表（已完成）
     * POST /site/columns
     *
     * @param siteId 站点ID
     * @return 网站栏目列表
     */
    public static JSONObject getSiteColumns(Integer siteId) {
        if (TEST_MODE) {
            return getSiteColumnsTestResponse(siteId);
        }

        try {
            JSONObject request = new JSONObject();
            request.put("site_id", siteId);

            return callApi("/site/columns", request);
        } catch (Exception e) {
            return createErrorResponse("获取网站栏目列表失败", e.getMessage());
        }
    }

    /**
     * 分页按条件获取指定站点集合死链列表（已完成）
     * POST /link/dead-links
     *
     * @param siteIds     站点ID列表（必填）
     * @param page        页码（必填）
     * @param perPage     每页数量（必填）
     * @param startTime   开始时间（必填，爬取时间）
     * @param endTime     结束时间（必填，爬取时间）
     * @param statusCodes 状态码列表（选填，默认状态码>=400为死链）
     * @return 死链列表
     */
    public static JSONObject getDeadLinks(List<Integer> siteIds, Integer page, Integer perPage,
                                          String startTime, String endTime, List<Integer> statusCodes) {
        if (TEST_MODE) {
            return getDeadLinksTestResponse();
        }

        try {
            JSONObject request = new JSONObject();
            request.put("site_ids", siteIds);
            request.put("page", page);
            request.put("per_page", perPage);

            JSONObject timeRange = new JSONObject();
            timeRange.put("start", startTime);
            timeRange.put("end", endTime);
            request.put("time_range", timeRange);

            if (statusCodes != null && !statusCodes.isEmpty()) {
                request.put("status_codes", statusCodes);
            }

            return callApi("/link/dead-links", request);
        } catch (Exception e) {
            return createErrorResponse("获取死链列表失败", e.getMessage());
        }
    }

    /**
     * 分页按条件获取指定站点集合死链列表（简化版本）
     *
     * @param siteIds   站点ID列表
     * @param page      页码
     * @param perPage   每页数量
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 死链列表
     */
    public static JSONObject getDeadLinks(List<Integer> siteIds, Integer page, Integer perPage,
                                          String startTime, String endTime) {
        return getDeadLinks(siteIds, page, perPage, startTime, endTime, null);
    }

    /**
     * 分页按条件获取指定站点集合异常外链列表（已完成）
     * POST /link/external-links
     *
     * @param siteIds     站点ID列表（必填）
     * @param page        页码（必填）
     * @param perPage     每页数量（必填）
     * @param startTime   开始时间（必填，爬取时间）
     * @param endTime     结束时间（必填，爬取时间）
     * @param statusCodes 状态码列表（选填，默认状态码>=400）
     * @return 异常外链列表
     */
    public static JSONObject getExternalLinks(List<Integer> siteIds, Integer page, Integer perPage,
                                              String startTime, String endTime, List<Integer> statusCodes) {
        if (TEST_MODE) {
            return getExternalLinksTestResponse();
        }

        try {
            JSONObject request = new JSONObject();
            request.put("site_ids", siteIds);
            request.put("page", page);
            request.put("per_page", perPage);

            JSONObject timeRange = new JSONObject();
            timeRange.put("start", startTime);
            timeRange.put("end", endTime);
            request.put("time_range", timeRange);

            if (statusCodes != null && !statusCodes.isEmpty()) {
                request.put("status_codes", statusCodes);
            }

            return callApi("/link/external-links", request);
        } catch (Exception e) {
            return createErrorResponse("获取异常外链列表失败", e.getMessage());
        }
    }

    /**
     * 分页按条件获取指定站点集合异常外链列表（简化版本）
     *
     * @param siteIds   站点ID列表
     * @param page      页码
     * @param perPage   每页数量
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 异常外链列表
     */
    public static JSONObject getExternalLinks(List<Integer> siteIds, Integer page, Integer perPage,
                                              String startTime, String endTime) {
        return getExternalLinks(siteIds, page, perPage, startTime, endTime, null);
    }

    // ==================== 私有工具方法 ====================

    /**
     * 调用采集中心API的通用方法
     *
     * @param endpoint    API端点
     * @param requestBody 请求体
     * @return API响应结果
     */
    private static JSONObject callApi(String endpoint, JSONObject requestBody) {
        try {
            String url = BASE_URL + endpoint;

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("X-DaheCollection-Key", API_KEY);
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<String> entity = new HttpEntity<>(requestBody.toJSONString(), headers);

            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                return JSONObject.parseObject(response.getBody());
            } else {
                return createErrorResponse("API调用失败", "HTTP状态码: " + response.getStatusCode());
            }
        } catch (Exception e) {
            return createErrorResponse("API调用异常", e.getMessage());
        }
    }

    /**
     * 创建错误响应
     *
     * @param message 错误消息
     * @param detail  错误详情
     * @return 错误响应JSON
     */
    private static JSONObject createErrorResponse(String message, String detail) {
        JSONObject response = new JSONObject();
        response.put("success", false);
        response.put("message", message);
        response.put("detail", detail);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }

    // ==================== 测试响应方法 ====================

    /**
     * 连通性检查测试响应
     */
    private static JSONObject getConnectivityTestResponse() {
        String jsonStr = "{\n" +
                "    \"success\": true,\n" +
                "    \"data\": [\n" +
                "        {\n" +
                "            \"url\": \"https://example.com\",\n" +
                "            \"status\": 200,\n" +
                "            \"latency_ms\": 150,\n" +
                "            \"timestamp\": \"2023-11-01T10:00:00Z\",\n" +
                "            \"error\": null\n" +
                "        },\n" +
                "        {\n" +
                "            \"url\": \"https://invalid.com\",\n" +
                "            \"status\": null,\n" +
                "            \"latency_ms\": 0,\n" +
                "            \"timestamp\": \"2023-11-01T10:00:00Z\",\n" +
                "            \"error\": {\n" +
                "                \"code\": \"CONNECTION_ERROR\",\n" +
                "                \"message\": \"Failed to establish connection\"\n" +
                "            }\n" +
                "        }\n" +
                "    ],\n" +
                "    \"metadata\": {\n" +
                "        \"total_urls\": 2,\n" +
                "        \"successful_checks\": 1,\n" +
                "        \"failed_checks\": 1,\n" +
                "        \"total_time_ms\": 200\n" +
                "    }\n" +
                "}";
        return JSONObject.parseObject(jsonStr);
    }

    /**
     * 首页更新情况测试响应
     */
    private static JSONObject getHomepageUpdatesTestResponse() {
        String jsonStr = "{\n" +
                "    \"success\": true,\n" +
                "    \"data\": [\n" +
                "        {\n" +
                "            \"site_id\": 102,\n" +
                "            \"url\": \"example.com\",\n" +
                "            \"status\": \"updated\",\n" +
                "            \"last_check\": \"2025-08-04T09:30:00Z\",\n" +
                "            \"latency_ms\": 320,\n" +
                "            \"error\": null\n" +
                "        },\n" +
                "        {\n" +
                "            \"site_id\": 103,\n" +
                "            \"url\": \"sample.org\",\n" +
                "            \"status\": \"unchanged\",\n" +
                "            \"last_check\": \"2025-08-02T18:45:00Z\",\n" +
                "            \"latency_ms\": 280,\n" +
                "            \"error\": null\n" +
                "        }\n" +
                "    ],\n" +
                "    \"metadata\": {\n" +
                "        \"total\": 2,\n" +
                "        \"updated\": 1,\n" +
                "        \"unchanged\": 1,\n" +
                "        \"errors\": 0\n" +
                "    }\n" +
                "}";
        return JSONObject.parseObject(jsonStr);
    }

    /**
     * 站点搜索测试响应
     */
    private static JSONObject getSearchSitesTestResponse(String name) {
        String jsonStr = "{\n" +
                "    \"query\": \"" + name + "\",\n" +
                "    \"site_id\": {\n" +
                "        \"1071\": \"河南省人民防空办公室\",\n" +
                "        \"1287\": \"河南省人民检察院\",\n" +
                "        \"401\": \"河南省人民政府外事办公室\",\n" +
                "        \"409\": \"河南省人民政府国有资产监督管理委员会\",\n" +
                "        \"4211\": \"河南省人民政府研究室\",\n" +
                "        \"4222\": \"河南省人民政府法制办公室\",\n" +
                "        \"4225\": \"河南省人民政府驻北京办事处\",\n" +
                "        \"4228\": \"河南省人民政府参事室\",\n" +
                "        \"6\": \"河南省人民政府\"\n" +
                "    },\n" +
                "    \"success\": true\n" +
                "}";
        return JSONObject.parseObject(jsonStr);
    }

    /**
     * 网站栏目列表测试响应
     */
    private static JSONObject getSiteColumnsTestResponse(Integer siteId) {
        String jsonStr = "{\n" +
                "    \"data\": {\n" +
                "        \"columns\": [\n" +
                "            {\n" +
                "                \"column_id\": 104739,\n" +
                "                \"name\": \"河南省人民政府驻北京办事处n9KP\",\n" +
                "                \"url\": \"http://www.henan.gov.cn/ztzx/fzjz\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"column_id\": 104776,\n" +
                "                \"name\": \"河南省人民政府驻北京办事处iMqF\",\n" +
                "                \"url\": \"http://www.henan.gov.cn/ywdt/sxdt\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"column_id\": 106151,\n" +
                "                \"name\": \"河南省人民政府驻北京办事处0aOp\",\n" +
                "                \"url\": \"http://www.henan.gov.cn/ywdt/gzdt\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"site_id\": " + siteId + ",\n" +
                "        \"site_name\": \"河南省人民政府驻北京办事处\"\n" +
                "    },\n" +
                "    \"metadata\": {\n" +
                "        \"total_columns\": 17\n" +
                "    },\n" +
                "    \"success\": true\n" +
                "}";
        return JSONObject.parseObject(jsonStr);
    }

    /**
     * 死链列表测试响应
     */
    private static JSONObject getDeadLinksTestResponse() {
        String jsonStr = "{\n" +
                "    \"success\": true,\n" +
                "    \"data\": {\n" +
                "        \"broken_links\": [\n" +
                "            {\n" +
                "                \"site_id\": 102,\n" +
                "                \"link_url\": \"https://example.com/missing-page\",\n" +
                "                \"link_name\": \"失败链接\",\n" +
                "                \"content_type\": \"text/html\",\n" +
                "                \"link_type\": \"internal\",\n" +
                "                \"parent_url\": \"https://example.com/home\",\n" +
                "                \"status_code\": 404,\n" +
                "                \"parent_pub_time\": \"2025-08-01T00:00:00Z\",\n" +
                "                \"parent_web_code\": \"<html> .... <html>\",\n" +
                "                \"collect_time\": \"2025-08-02T00:00:00Z\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"pagination\": {\n" +
                "            \"total\": 1,\n" +
                "            \"page\": 1,\n" +
                "            \"per_page\": 20,\n" +
                "            \"total_pages\": 1\n" +
                "        }\n" +
                "    },\n" +
                "    \"metadata\": {\n" +
                "        \"total_sites\": 1\n" +
                "    }\n" +
                "}";
        return JSONObject.parseObject(jsonStr);
    }

    /**
     * 异常外链列表测试响应
     */
    private static JSONObject getExternalLinksTestResponse() {
        String jsonStr = "{\n" +
                "    \"success\": true,\n" +
                "    \"data\": {\n" +
                "        \"external_links\": [\n" +
                "            {\n" +
                "                \"site_id\": 102,\n" +
                "                \"link_url\": \"https://example.com/outpage\",\n" +
                "                \"link_name\": \"失败链接\",\n" +
                "                \"content_type\": \"text/html\",\n" +
                "                \"link_type\": \"normal\",\n" +
                "                \"parent_url\": \"https://hadu.com/home\",\n" +
                "                \"status_code\": 200,\n" +
                "                \"web_code\": \"<html> .... <html>\",\n" +
                "                \"collect_time\": \"2025-08-02T00:00:00Z\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"pagination\": {\n" +
                "            \"total\": 1,\n" +
                "            \"page\": 1,\n" +
                "            \"per_page\": 20,\n" +
                "            \"total_pages\": 1\n" +
                "        }\n" +
                "    },\n" +
                "    \"metadata\": {\n" +
                "        \"total_sites\": 1\n" +
                "    }\n" +
                "}";
        return JSONObject.parseObject(jsonStr);
    }

    public static void main(String[] args) {
        JSONObject 河南省人民政府 = searchSitesByName("河南理工大学");
        System.out.println(JSONObject.toJSONString(河南省人民政府));
    }

}
