//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package cn.dahe.utils.ees;

import org.apache.http.Header;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicHeader;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;

public class HttpUtil {
    private static final String NL = System.getProperty("line.separator");
    private static CloseableHttpClient httpClient;

    private HttpUtil() {
    }

    public static String get(String url) {
        HttpGet httpGet = new HttpGet(url);
        return getResponseString(httpGet);
    }

    public static String post(String url, String jsonString) {
        HttpPost httpPost = new HttpPost(url);
        httpPost.setEntity(new StringEntity(jsonString, Charset.forName("UTF-8")));
        return getResponseString(httpPost);
    }

    private static String getResponseString(HttpRequestBase request) {
        String result = "";

        try {
            CloseableHttpResponse response = httpClient.execute(request);
            Throwable var3 = null;

            try {
                BufferedReader in = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));
                Throwable var5 = null;

                try {
                    StringBuilder sb = new StringBuilder();

                    String line;
                    while((line = in.readLine()) != null) {
                        sb.append(line + NL);
                    }

                    result = sb.toString();
                } catch (Throwable var31) {
                    var5 = var31;
                    throw var31;
                } finally {
                    if (in != null) {
                        if (var5 != null) {
                            try {
                                in.close();
                            } catch (Throwable var30) {
                                var5.addSuppressed(var30);
                            }
                        } else {
                            in.close();
                        }
                    }

                }
            } catch (Throwable var33) {
                var3 = var33;
                throw var33;
            } finally {
                if (response != null) {
                    if (var3 != null) {
                        try {
                            response.close();
                        } catch (Throwable var29) {
                            var3.addSuppressed(var29);
                        }
                    } else {
                        response.close();
                    }
                }

            }
        } catch (IOException var35) {
            var35.printStackTrace();
        }

        return result;
    }

    static {
        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(30000).setConnectionRequestTimeout(30000).setSocketTimeout(30000).build();
        List<Header> defaultHeaders = new ArrayList();
        defaultHeaders.add(new BasicHeader("Content-type", "application/json; charset=utf-8"));
        defaultHeaders.add(new BasicHeader("Accept", "application/json"));
        httpClient = HttpClientBuilder.create().setMaxConnTotal(100).setMaxConnPerRoute(50).setDefaultRequestConfig(requestConfig).setDefaultHeaders(defaultHeaders).build();
    }
}
