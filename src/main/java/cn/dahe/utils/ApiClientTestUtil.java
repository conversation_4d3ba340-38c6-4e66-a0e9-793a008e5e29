package cn.dahe.utils;

import cn.hutool.http.HttpUtil;

import java.security.MessageDigest;
import java.util.HashMap;
import java.util.Map;

public class ApiClientTestUtil {
    private static final String API_KEY = "collection_center_api_key";
    private static final String SECRET = "dahe_collection_center_2025";

    public static void main(String[] args) {
        ApiClientTestUtil client = new ApiClientTestUtil();
        client.callPushDataApi("your_data_here");
    }

    public void callPushDataApi(String data) {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String nonce = generateNonce();
        String signature = generateSignature("POST", "/platform-api/pro/chk-all-site-search/receive-push-data",
                API_KEY, timestamp, nonce);

        Map<String, String> headers = new HashMap<>();
        headers.put("X-API-Key", API_KEY);
        headers.put("X-Timestamp", timestamp);
        headers.put("X-Nonce", nonce);
        headers.put("X-Signature", signature);
        headers.put("Authorization", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiI3ODEiLCJpYXQiOjE3NTQyOTI1MDd9.EvwLAi4H-8Gl82_-4GE8dbwwkf8B6h4FKRUSguUybU0");

        String body = HttpUtil.createPost("http://localhost:8081/platform-api/pro/chk-all-site-search/receive-push-data")
                .addHeaders(headers)
                .body(data)
                .execute().body();
        System.out.println(body);
    }

    private String generateSignature(String method, String uri, String apiKey,
                                     String timestamp, String nonce) {
        String signStr = method + uri + apiKey + timestamp + nonce + SECRET;
        return md5(signStr).toUpperCase();
    }

    private String generateNonce() {
        return java.util.UUID.randomUUID().toString().replace("-", "");
    }

    private String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(input.getBytes("UTF-8"));
            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
