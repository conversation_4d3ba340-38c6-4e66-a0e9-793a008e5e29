package cn.dahe.utils;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import lombok.extern.slf4j.Slf4j;

import java.net.SocketTimeoutException;
import java.util.Map;

/**
 * HTTP工具类
 */
@Slf4j
public class HttpUtil {

    /**
     * 发送POST请求
     *
     * @param url 请求URL
     * @param params 请求参数
     * @param timeout 超时时间（毫秒）
     * @return 响应内容
     */
    public static String post(String url, Map<String, Object> params, int timeout) throws SocketTimeoutException {
        try {
            HttpResponse response = HttpRequest.post(url)
                    .form(params)
                    .timeout(timeout)
                    .execute();

            if (!response.isOk()) {
                throw new RuntimeException("HTTP请求失败，状态码：" + response.getStatus());
            }

            return response.body();
        } catch (Exception e) {
            log.error("HTTP请求失败：{}", e.getMessage());
            throw e;
        }
    }
}