package cn.dahe.utils;

import cn.hutool.crypto.symmetric.SymmetricCrypto;

/**
 * <AUTHOR>
 * @date 2024-02-27
 */
public class AESUtils {

    private static final String KEY = "MyFixedKey123456";

    public static String encrypt(String plaintext) {
        // 创建 SymmetricCrypto 对象，指定密钥
        SymmetricCrypto aes = new SymmetricCrypto("AES", KEY.getBytes());
        // 加密文本并返回加密后的 Base64 编码字符串
        return aes.encryptBase64(plaintext);
    }

    public static String decrypt(String encryptedText) {
        // 创建 SymmetricCrypto 对象，指定密钥
        SymmetricCrypto aes = new SymmetricCrypto("AES", KEY.getBytes());
        // 解密 Base64 编码的密文并返回原始字符串
        return aes.decryptStr(encryptedText);
    }

    public static void main(String[] args) {
        String plaintext = "Hello, world!";
        String encryptedText = encrypt(plaintext);
        System.out.println("Encrypted text: " + encryptedText);
        String decryptedText = decrypt(encryptedText);
        System.out.println("Decrypted text: " + decryptedText);
    }
}
