package cn.dahe.utils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-08-07
 */
public class ListUtils {


    public static List<String> transferIdsToList(String ids) {
        List<String> collect = new ArrayList<>();
        if (StringUtils.isNotBlank(ids)) {
            collect = Arrays.stream(ids.split(",")).map(String::trim).distinct().collect(Collectors.toList());
        }
        return collect;
    }

}
