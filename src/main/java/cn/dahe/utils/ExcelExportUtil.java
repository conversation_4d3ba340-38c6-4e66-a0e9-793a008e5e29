package cn.dahe.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.function.Function;

/**
 * Excel导出工具类
 * 提供通用的Excel导出功能
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
public class ExcelExportUtil {

    /**
     * 通用Excel导出方法
     *
     * @param response    HTTP响应对象
     * @param fileName    文件名（不包含扩展名）
     * @param sheetName   工作表名称
     * @param headers     表头数组
     * @param dataList    数据列表
     * @param dataMapper  数据映射函数，将数据对象转换为字符串数组
     * @param <T>         数据类型
     * @throws IOException 导出异常
     */
    public static <T> void exportToExcel(HttpServletResponse response,
                                        String fileName,
                                        String sheetName,
                                        String[] headers,
                                        List<T> dataList,
                                        Function<T, String[]> dataMapper) throws IOException {
        try {
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String encodedFileName = URLEncoder.encode(fileName + "_" + System.currentTimeMillis(), "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");

            // 创建Excel工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet(sheetName);

            // 创建标题行
            Row headerRow = sheet.createRow(0);
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }

            // 填充数据
            for (int i = 0; i < dataList.size(); i++) {
                Row row = sheet.createRow(i + 1);
                T data = dataList.get(i);
                String[] rowData = dataMapper.apply(data);
                
                for (int j = 0; j < rowData.length; j++) {
                    Cell cell = row.createCell(j);
                    cell.setCellValue(rowData[j] != null ? rowData[j] : "");
                }
            }

            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // 写入响应流
            workbook.write(response.getOutputStream());
            workbook.close();

        } catch (Exception e) {
            log.error("导出Excel失败", e);
            throw new IOException("导出失败：" + e.getMessage());
        }
    }

    /**
     * 格式化日期为字符串
     *
     * @param date 日期
     * @return 格式化后的日期字符串
     */
    public static String formatDate(Date date) {
        if (date == null) {
            return "";
        }
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date);
    }

    /**
     * 格式化日期为字符串（仅日期）
     *
     * @param date 日期
     * @return 格式化后的日期字符串
     */
    public static String formatDateOnly(Date date) {
        if (date == null) {
            return "";
        }
        return new SimpleDateFormat("yyyy-MM-dd").format(date);
    }

    /**
     * 安全获取字符串值
     *
     * @param value 值
     * @return 字符串值，null时返回空字符串
     */
    public static String safeString(Object value) {
        return value != null ? value.toString() : "";
    }
}
