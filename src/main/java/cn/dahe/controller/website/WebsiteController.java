package cn.dahe.controller.website;

import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.model.dto.Result;
import cn.dahe.entity.Website;
import cn.dahe.enums.CheckStrategyEnum;
import cn.dahe.model.query.WebsiteQuery;
import cn.dahe.service.WebsiteGroupService;
import cn.dahe.service.WebsiteService;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.model.vo.WebsiteGroupVO;
import cn.dahe.model.vo.WebsiteVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 网站信息管理
 */
@RestController
@RequestMapping("/pro/web-site")
@AllArgsConstructor
@Tag(name = "网站管理模块")
public class WebsiteController {

    @Resource
    private WebsiteService websiteService;

    @Resource
    private WebsiteGroupService websiteGroupService;

    @Operation(summary = "分页查询网站列表")
    @PostMapping("list")
    public Result page(WebsiteQuery query) {
        return Result.ok(websiteService.page(query));
    }

    // 分页-按照状态
    @PostMapping("list-by-status")
    public Result listByStatus(int status) {
        return Result.ok(websiteService.listByStatus(status));
    }

    // 添加
    @PostMapping("save")
    public Result save(Website vo, @CurrentUser LoginUserVO user) {
        return websiteService.save(vo, user);
    }

    // 添加
    @PostMapping("update")
    public Result update(Website vo, @CurrentUser LoginUserVO user) {
        return websiteService.update(vo, user);
    }

    // 修改状态
    @PostMapping("update-status")
    public Result updateStatus(@RequestParam(defaultValue = "") String id,
                               @CurrentUser LoginUserVO user) {
        return websiteService.updateStatus(id, user);
    }


    @Operation(summary = "查询所有可用站点列表")
    @PostMapping("total")
    public Result<List<WebsiteVO>> total() {
        return Result.ok(websiteService.listTotal());
    }

    @Operation(summary = "按分组查询所有可用站点列表")
    @PostMapping("group/total")
    public Result<List<WebsiteGroupVO>> listByGroup() {
        return Result.ok(websiteGroupService.listWithWebsites());
    }

    @Operation(summary = "更新网站巡查精准度")
    @PostMapping("update/check-strategy")
    public Result<String> updateCheckStrategy(@RequestParam  List<Long> websiteIds,
                                              @RequestParam Integer checkStrategy) {
        if (!CheckStrategyEnum.isValid(checkStrategy)) {
            return Result.error("参数不合法");
        }
        boolean success = websiteService.updateCheckStrategy(websiteIds, checkStrategy);
        return success ? Result.ok("更新成功") : Result.error("更新失败");
    }

}
