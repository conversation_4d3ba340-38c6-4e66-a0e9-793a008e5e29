package cn.dahe.controller.website;

import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.model.dto.Result;
import cn.dahe.entity.WebsiteGroup;
import cn.dahe.service.WebsiteGroupService;
import cn.dahe.model.vo.LoginUserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 网站分组管理
 */
@RestController
@RequestMapping("/pro/website-group")
@AllArgsConstructor
@Tag(name = "网站分组管理模块")
public class WebsiteGroupController {

    @Resource
    private WebsiteGroupService websiteGroupService;

    @Operation(summary = "获取所有分组列表")
    @PostMapping("list")
    public Result<List<WebsiteGroup>> list() {
        return Result.ok(websiteGroupService.listAll());
    }

    @Operation(summary = "添加分组")
    @PostMapping("save")
    public Result<String> save(WebsiteGroup group, @CurrentUser LoginUserVO user) {
        return websiteGroupService.save(group, user);
    }

    @Operation(summary = "删除分组")
    @PostMapping("delete")
    public Result<String> delete(@RequestParam Integer id, @CurrentUser LoginUserVO user) {
        return websiteGroupService.delete(id, user);
    }
} 