package cn.dahe.controller;

import cn.dahe.common.annotation.ApiAuth;
import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.entity.ChkUpdateSiteIndex;
import cn.dahe.model.query.ChkUpdateSiteIndexQuery;
import cn.dahe.service.ChkUpdateSiteIndexService;
import cn.dahe.model.vo.ChkUpdateSiteIndexVO;
import cn.dahe.model.vo.LoginUserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * 首页更新检查Controller - 专注于首页更新检查功能
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@RestController
@RequestMapping("/pro/chk-update-site-index")
@AllArgsConstructor
@Tag(name = "首页更新检查")
public class ChkUpdateSiteIndexController {

    @Resource
    private ChkUpdateSiteIndexService chkUpdateSiteIndexService;

    // ==================== 首页更新检查概览 ====================

    @Operation(summary = "获取首页更新检查概览统计", description = "获取检测网站数、更新网站、未更新网站统计数据")
    @PostMapping("overview-statistics")
    public Result<Map<String, Object>> getOverviewStatistics(
            @Parameter(description = "查询参数") ChkUpdateSiteIndexQuery query,
            @CurrentUser LoginUserVO user) {
        return Result.ok(chkUpdateSiteIndexService.getOverviewStatistics(query));
    }

    // ==================== 首页更新检查记录 ====================

    @Operation(summary = "分页查询站点首页更新结果", description = "分页查询站点首页更新结果")
    @PostMapping("page")
    public Result<PageResult<ChkUpdateSiteIndexVO>> page(
            @Parameter(description = "查询参数") ChkUpdateSiteIndexQuery query,
            @CurrentUser LoginUserVO user) {
        return Result.ok(chkUpdateSiteIndexService.page(query));
    }

    @Operation(summary = "分页查询首页更新的文章列表", description = "分页查询chk_update_site_index表中的详细记录")
    @PostMapping("/detail")
    public Result<PageResult<ChkUpdateSiteIndex>> detail(
            @Parameter(description = "查询参数") ChkUpdateSiteIndexQuery query,
            @CurrentUser LoginUserVO user) {
        return Result.ok(chkUpdateSiteIndexService.pageDetail(query));
    }

    // ==================== 数据导出 ====================

    @Operation(summary = "导出检查记录", description = "导出首页更新检查记录到Excel")
    @GetMapping("export")
    public void export(
            @Parameter(description = "分组ID（逗号分隔）", example = "1,2,3") @RequestParam(required = false) String groupId,
            @Parameter(description = "分组名称（逗号分隔）", example = "站1,站2,站3") @RequestParam(required = false) String groupName,
            @Parameter(description = "网站ID（逗号分隔）", example = "1,2,3") @RequestParam(required = false) String websiteId,
            @Parameter(description = "网站名称（逗号分隔）", example = "名称1,名称2,名称3") @RequestParam(required = false) String websiteName,
            @Parameter(description = "网站地址（逗号分隔）", example = "https://www.a1.cn,https://www.a2.cn") @RequestParam(required = false) String websiteIndexUrl,
            @Parameter(description = "解析开始时间", example = "2025-07-01 00:00:00") @RequestParam(required = false) String beginTime,
            @Parameter(description = "解析结束时间", example = "2025-07-31 23:59:59") @RequestParam(required = false) String endTime,
            @CurrentUser LoginUserVO user,
            HttpServletResponse response) throws IOException {

        ChkUpdateSiteIndexQuery query = new ChkUpdateSiteIndexQuery();
        query.setGroupId(groupId != null ? groupId : "");
        query.setGroupName(groupName != null ? groupName : "");
        query.setWebsiteId(websiteId != null ? websiteId : "");
        query.setWebsiteName(websiteName != null ? websiteName : "");
        query.setWebsiteIndexUrl(websiteIndexUrl != null ? websiteIndexUrl : "");
        query.setBeginTime(beginTime != null ? beginTime : "");
        query.setEndTime(endTime != null ? endTime : "");

        chkUpdateSiteIndexService.export(query, user, response);
    }

    @Operation(summary = "接收推送首页更新数据", description = "被动接收采集中心推送的首页更新检查数据并初始化表数据")
    @ApiAuth(value = "collection_center", description = "采集中心推送数据认证")
    @PostMapping("receive-push-data")
    public Result<String> receivePushData(
            @Parameter(description = "推送的首页更新数据") @RequestBody String pushData) {
        log.info("接收到采集中心推送的首页更新检查数据");

        try {
            // TODO: 实现接收推送数据的逻辑
            // 1. 解析推送的JSON数据
            // 2. 验证数据格式和完整性
            // 3. 转换为ChkUpdateSiteIndex实体
            // 4. 批量插入或更新数据库
            // 5. 设置检测状态和解析时间

            log.info("接收推送首页更新检查数据成功，数据长度：{}", pushData != null ? pushData.length() : 0);
            return Result.ok("接收推送数据成功，共处理0条记录");
        } catch (Exception e) {
            log.error("接收推送首页更新检查数据失败", e);
            return Result.error("接收推送数据失败：" + e.getMessage());
        }
    }
}
