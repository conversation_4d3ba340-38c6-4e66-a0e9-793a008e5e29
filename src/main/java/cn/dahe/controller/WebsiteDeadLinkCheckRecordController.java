package cn.dahe.controller;

import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.model.dto.*;
import cn.dahe.entity.WebsiteDeadLinkCheckRecord;
import cn.dahe.model.query.WebsiteDeadLinkCheckRecordQuery;
import cn.dahe.service.WebsiteDeadLinkCheckRecordService;
import cn.dahe.model.vo.LoginUserVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025-07-08
 */
@RestController
@RequestMapping("/pro/web/dead-link")
@AllArgsConstructor
@Tag(name= "网站连通性检查模块")
public class WebsiteDeadLinkCheckRecordController {

    @Resource
    private WebsiteDeadLinkCheckRecordService websiteDeadLinkCheckRecordService;


    //分页-死链详情
    @PostMapping("page-dead-link-detail")
    public Result<PageResult<WebsiteDeadLinkCheckRecordDto>> pageDeadLinkDetail(WebsiteDeadLinkCheckRecordQuery query, @CurrentUser LoginUserVO user) {
        return Result.ok(websiteDeadLinkCheckRecordService.pageDeadLinkDetail(query));
    }

    //分页-检查详情
    @PostMapping("page-check-record-detail")
    public Result<PageResult<WebsiteDeadLinkCheckRecord>> pageCheckRecordDetail(WebsiteDeadLinkCheckRecordQuery query, @CurrentUser LoginUserVO user) {
        return Result.ok(websiteDeadLinkCheckRecordService.pageCheckRecordDetail(query));
    }


    /**
     * 死链统计 - 总体概览
     */
    @PostMapping("total-overview")
    public Result<WebsiteUpdateStatsDto> getDeadLinkOverview(WebsiteDeadLinkCheckRecordQuery query,
                                                             @CurrentUser LoginUserVO user) {
        return Result.ok(websiteDeadLinkCheckRecordService.totalOverview(query));
    }

    /**
     * 死链统计 - 网站分页统计详情
     */
    @PostMapping("/page-website-stats")
    public Result<PageResult<WebsiteAccessOverviewDto>> getWebsiteDeadLinkStatsPage(WebsiteDeadLinkCheckRecordQuery query,
                                                                                    @CurrentUser LoginUserVO user) {
        return Result.ok(websiteDeadLinkCheckRecordService.pageWebsiteStats(query));
    }

}
