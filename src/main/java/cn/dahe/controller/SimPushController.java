package cn.dahe.controller;

import cn.dahe.model.message.ArticleMessage;
import cn.dahe.service.message.ArticleMessageProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;

/**
 * 文章校对控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/push")
public class SimPushController {

    @Autowired
    private ArticleMessageProducer articleMessageProducer;

    /**
     * 接收文章推送
     */
    @PostMapping("/article/push")
    public String receiveArticle(ArticleMessage articleMessage) {
        try {
            // 设置创建时间和初始重试次数
            articleMessage.setCreateTime(LocalDateTime.now());
            articleMessage.setRetryCount(0);

            // 发送到消息队列
            String messageId = articleMessageProducer.sendMessage(articleMessage);
            log.info("接收文章推送成功: articleId={}, messageId={}", articleMessage.getArticleId(), messageId);
            
            return messageId;
        } catch (Exception e) {
            log.error("接收文章推送失败: {}", articleMessage, e);
            throw new RuntimeException("接收文章推送失败", e);
        }
    }
}