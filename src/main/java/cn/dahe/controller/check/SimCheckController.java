package cn.dahe.controller.check;

import cn.dahe.model.dto.*;
import cn.dahe.entity.Article;
import cn.dahe.service.CheckTaskService;
import cn.dahe.service.CheckResultService;
import cn.dahe.service.ArticleService;
import cn.dahe.service.SimCheckService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Tag(name = "模拟校对")
@Slf4j
@RestController
//  修改前缀，回避登录限制
@RequestMapping("/sim/check")
public class SimCheckController {

    @Resource
    private ArticleService articleService;
    @Resource
    private SimCheckService simCheckService;
    @Resource
    private CheckTaskService checkTaskService;


    @Resource
    private CheckResultService checkResultService;


    @Data
    public static class ContentCheckRequest {
        private String title;
        private String content;
    }


    @Operation(summary = "模拟校对接口")
    @PostMapping("/check")
    public Result<List<ArticleCheckDto>> simulateCheck(
            @Parameter(description = "校对请求") ContentCheckRequest request) {
        List<ArticleCheckDto> results = simCheckService.simulateCheck(request.getTitle(), request.getContent());
        return Result.ok(results);
    }


    @Operation(summary = "模拟推送文章")
    @PostMapping("/push")
    public Result<SimCheckService.PushArticle> simulatePush() {
        SimCheckService.PushArticle article = simCheckService.pushRandomArticle();
        // TODO 接收
        return receivePush(article);
    }

    @Operation(summary = "接收推送文章")
    @PostMapping("/receive")
    public Result<SimCheckService.PushArticle> receivePush(@Parameter(description = "推送的文章") SimCheckService.PushArticle article) {
        // 1. 保存Article
        Long websiteId = article.getWebsiteId();
        ArticleSaveDto saveDTO = new ArticleSaveDto()
                .setWebsiteId(websiteId)
                .setTitle(article.getTitle())
                .setUrl(article.getUrl())
                .setPubTime(article.getPubTime());
        Article newArticle = articleService.savePushArticle(saveDTO);
        Long articleId = newArticle.getId();
        // 2. 处理并保存文章内容（即使校对失败也能保留内容） TODO 异步
        ArticleCheckExecuteDto checkExecuteDto = checkTaskService.initArticleCheckTask(
                newArticle,
                article.getTitle(),
                article.getContent()
        );

        // 3. 执行内容校对
        ContentCheckResultDto checkResult = checkResultService.executeContentCheck(
                checkExecuteDto.getCheckTask(),
                checkExecuteDto.getCheckContent()
        );
        // 4. 处理校对结果
        checkTaskService.processCheckResults(articleId, checkExecuteDto, checkResult);
        return Result.ok(article);
    }


}
