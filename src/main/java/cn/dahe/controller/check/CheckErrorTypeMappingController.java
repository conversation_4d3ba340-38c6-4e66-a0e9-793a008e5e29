package cn.dahe.controller.check;

import cn.dahe.model.dto.Result;
import cn.dahe.entity.CheckErrorTypeMapping;
import cn.dahe.service.CheckErrorTypeMappingService;
import cn.dahe.model.vo.check.CheckErrorTypeMappingVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 错误类型映射Controller
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@RestController
@RequestMapping("/pro/check/error-type-mapping")
public class CheckErrorTypeMappingController {

    @Autowired
    private CheckErrorTypeMappingService checkErrorTypeMappingService;

    /**
     * 获取映射关系列表
     */
    @GetMapping("/list")
    public Result<List<CheckErrorTypeMappingVO>> getMappingList() {
        return Result.ok(checkErrorTypeMappingService.getMappingList());
    }

    /**
     * 根据来源和错误码获取映射关系
     */
    @GetMapping("/source")
    public Result<CheckErrorTypeMappingVO> getMappingBySourceError(
            @RequestParam String sourceCode,
            @RequestParam String sourceErrorCode) {
        return Result.ok(checkErrorTypeMappingService.getMappingBySourceError(sourceCode, sourceErrorCode));
    }

    /**
     * 新增映射关系
     */
    @PostMapping
    public Result<CheckErrorTypeMapping> save(@RequestBody CheckErrorTypeMapping mapping) {
        checkErrorTypeMappingService.save(mapping);
        return Result.ok(mapping);
    }

    /**
     * 批量保存映射关系
     */
    @PostMapping("/batch")
    public Result<Boolean> saveBatch(@RequestBody List<CheckErrorTypeMapping> mappings) {
        return Result.ok(checkErrorTypeMappingService.saveBatchMapping(mappings));
    }

    /**
     * 更新映射关系
     */
    @PutMapping("/{id}")
    public Result<Boolean> update(@PathVariable Long id, @RequestBody CheckErrorTypeMapping mapping) {
        mapping.setId(id);
        return Result.ok(checkErrorTypeMappingService.updateById(mapping));
    }

    /**
     * 删除映射关系
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> delete(@PathVariable Long id) {
        return Result.ok(checkErrorTypeMappingService.removeById(id));
    }
} 