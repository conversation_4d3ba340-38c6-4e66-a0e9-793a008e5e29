package cn.dahe.controller.attachment;

import cn.dahe.common.annotation.ApiAuth;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.model.query.AttachmentCheckQuery;
import cn.dahe.service.AttachmentService;
import cn.dahe.model.vo.AttachmentCheckVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * 附件检查Controller - 对应原型图功能
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@RestController
@RequestMapping("/pro/attachment-check")
@AllArgsConstructor
@Tag(name = "附件检查管理")
public class AttachmentCheckController {

    @Resource
    private AttachmentService attachmentService;

    @Operation(summary = "分页查询附件检查记录", description = "获取附件检查记录列表，包含网站名称、来源网站、来源页面等信息")
    @PostMapping("page")
    public Result<PageResult<AttachmentCheckVO>> page(AttachmentCheckQuery query) {
        return Result.ok(attachmentService.pageAttachmentAndCheckResults(query));
    }

    @Operation(summary = "获取附件检查详情", description = "根据ID获取附件检查详细信息")
    @PostMapping("info/{attachmentId}")
    public Result<AttachmentCheckVO> info(@PathVariable("attachmentId") Long attachmentId, AttachmentCheckQuery query) {
        return Result.ok(attachmentService.getAttachmentAndCheckResults(attachmentId,query));
    }

    // ==================== 数据导出 ====================

    @Operation(summary = "导出附件检查记录", description = "导出附件检查记录到Excel")
    @GetMapping("export")
    public void export(AttachmentCheckQuery query) throws IOException {
        attachmentService.export(query);
    }

    @Operation(summary = "接收推送附件数据", description = "被动接收采集中心推送的附件检查数据并初始化表数据")
    @ApiAuth(value = "collection_center", description = "采集中心推送数据认证")
    @PostMapping("receive-push-data")
    public Result<String> receivePushData(
            @Parameter(description = "推送的附件数据") @RequestBody String pushData) {
        log.info("接收到采集中心推送的附件检查数据");

        try {
            // TODO: 实现接收推送数据的逻辑
            // 1. 解析推送的JSON数据
            // 2. 验证数据格式和完整性
            // 3. 转换为ChkAttachUrl实体
            // 4. 批量插入或更新数据库
            // 5. 设置检测状态和时间

            log.info("接收推送附件检查数据成功，数据长度：{}", pushData != null ? pushData.length() : 0);
            return Result.ok("接收推送数据成功，共处理0条记录");
        } catch (Exception e) {
            log.error("接收推送附件检查数据失败", e);
            return Result.error("接收推送数据失败：" + e.getMessage());
        }
    }
}
