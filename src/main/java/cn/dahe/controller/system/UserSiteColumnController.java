package cn.dahe.controller.system;

import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.model.query.UserSiteColumnQuery;
import cn.dahe.service.UserSiteColumnService;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.model.vo.UserSiteColumnVO;
import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 用户站点栏目分配Controller
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Slf4j
@RestController
@RequestMapping("/system/user-site-column")
@RequiredArgsConstructor
@Tag(name = "用户站点栏目分配管理", description = "用户站点栏目分配相关接口")
public class UserSiteColumnController {

    private final UserSiteColumnService userSiteColumnService;

    @Operation(summary = "分页查询用户站点栏目分配", description = "分页查询用户站点栏目分配列表")
    @PostMapping("/page")
    public Result<PageResult<UserSiteColumnVO>> page(
            @Parameter(description = "查询参数") @Valid UserSiteColumnQuery query,
            @Parameter(hidden = true) @CurrentUser LoginUserVO user) {
        return Result.ok(userSiteColumnService.page(query));
    }

    @Operation(summary = "搜索站点列表", description = "根据站点名称关键词搜索站点列表")
    @GetMapping("/sites/search")
    public Result<JSONObject> searchSites(
            @Parameter(description = "站点名称关键词") @RequestParam(required = false) String siteName,
            @Parameter(hidden = true) @CurrentUser LoginUserVO user) {
        JSONObject result = userSiteColumnService.getSiteList(siteName);
        return Result.ok(result);
    }

    @Operation(summary = "获取站点栏目列表", description = "根据站点ID获取栏目列表")
    @GetMapping("/sites/{siteId}/columns")
    public Result<JSONObject> getSiteColumns(
            @Parameter(description = "站点ID") @PathVariable Integer siteId,
            @Parameter(hidden = true) @CurrentUser LoginUserVO user) {
        JSONObject result = userSiteColumnService.getSiteColumns(siteId);
        return Result.ok(result);
    }

    @Operation(summary = "获取用户已分配的站点列表", description = "获取指定用户已分配的站点列表")
    @GetMapping("/users/{userId}/sites")
    public Result<List<UserSiteColumnVO>> getUserAssignedSites(
            @Parameter(description = "用户ID") @PathVariable Integer userId,
            @Parameter(hidden = true) @CurrentUser LoginUserVO user) {
        List<UserSiteColumnVO> result = userSiteColumnService.getUserAssignedSites(userId);
        return Result.ok(result);
    }

    @Operation(summary = "获取用户在指定站点下已分配的栏目列表", description = "获取用户在指定站点下已分配的栏目列表")
    @GetMapping("/users/{userId}/sites/{siteId}/columns")
    public Result<List<UserSiteColumnVO>> getUserAssignedColumns(
            @Parameter(description = "用户ID") @PathVariable Integer userId,
            @Parameter(description = "站点ID") @PathVariable Integer siteId,
            @Parameter(hidden = true) @CurrentUser LoginUserVO user) {
        List<UserSiteColumnVO> result = userSiteColumnService.getUserAssignedColumns(userId, siteId);
        return Result.ok(result);
    }

    @Operation(summary = "为用户分配站点栏目", description = "为指定用户分配站点和栏目")
    @PostMapping("/users/{userId}/assign")
    public Result<Boolean> assignSiteColumns(
            @Parameter(description = "用户ID") @PathVariable Integer userId,
            @Parameter(description = "分配信息列表") @RequestBody @Valid List<UserSiteColumnService.SiteColumnAssignVO> assignments,
            @Parameter(hidden = true) @CurrentUser LoginUserVO user) {
        boolean result = userSiteColumnService.assignSiteColumns(
            userId,
            assignments,
            user.getUserId() != null ? user.getUserId().toString() : "system",
            user.getTruename() != null ? user.getTruename() : "系统"
        );
        return Result.ok(result);
    }

    @Operation(summary = "删除用户的站点分配", description = "删除用户的整个站点分配（包括所有栏目）")
    @DeleteMapping("/users/{userId}/sites")
    public Result<Boolean> removeUserSiteAssignments(
            @Parameter(description = "用户ID") @PathVariable Integer userId,
            @Parameter(description = "站点ID列表") @RequestBody List<Integer> siteIds,
            @Parameter(hidden = true) @CurrentUser LoginUserVO user) {
        boolean result = userSiteColumnService.removeUserSiteAssignments(userId, siteIds);
        return Result.ok(result);
    }

    @Operation(summary = "删除用户的特定站点栏目分配", description = "删除用户在指定站点下的特定栏目分配")
    @DeleteMapping("/users/{userId}/sites/{siteId}/columns")
    public Result<Boolean> removeUserSiteColumnAssignments(
            @Parameter(description = "用户ID") @PathVariable Integer userId,
            @Parameter(description = "站点ID") @PathVariable Integer siteId,
            @Parameter(description = "栏目ID列表") @RequestBody List<Integer> columnIds,
            @Parameter(hidden = true) @CurrentUser LoginUserVO user) {
        boolean result = userSiteColumnService.removeUserSiteColumnAssignments(userId, siteId, columnIds);
        return Result.ok(result);
    }
}
