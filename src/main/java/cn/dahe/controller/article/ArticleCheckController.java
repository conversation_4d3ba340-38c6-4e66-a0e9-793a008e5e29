package cn.dahe.controller.article;

import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.enums.AuditStatusEnum;
import cn.dahe.model.query.ArticleCheckQuery;
import cn.dahe.service.ArticleService;
import cn.dahe.service.CheckResultService;
import cn.dahe.model.vo.ArticleCheckVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 文章检查控制器
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Tag(name = "内容巡检")
@RestController
@RequestMapping("/pro/article-check")
public class ArticleCheckController {

    @Resource
    private CheckResultService checkResultService;
    @Resource
    private ArticleService articleService;

    @Operation(summary = "分页查询文章内容及检查结果")
    @PostMapping(value = "/page")
    public Result<PageResult<ArticleCheckVO>> pageArticleChecks(ArticleCheckQuery query) {
        IPage<ArticleCheckVO> result = articleService.pageArticleAndCheckResults(query);
        return Result.ok(PageResult.page(result));
    }

    @Operation(summary = "获取文章内容及检查结果")
    @GetMapping("/info/{articleId}")
    public Result<ArticleCheckVO> getArticleChecks(@PathVariable("articleId") Long articleId, ArticleCheckQuery query) {
        return Result.ok(articleService.getArticleAndCheckResults(articleId, query));
    }

    @Operation(summary = "错词审核-驳回")
    @PostMapping("audit-reject")
    public Result<String> updateAuditStatusReject(@RequestParam Long articleId,
                                                  @Parameter(description = "检查ID列表，逗号分隔") @RequestParam List<Long> resultIds) {
        Boolean success = checkResultService.updateAuditStatus(articleId, resultIds, AuditStatusEnum.REJECT);
        return success ? Result.ok("操作成功") : Result.error("操作失败");
    }

    @Operation(summary = "错词审核-同意")
    @PostMapping("audit-pass")
    public Result<String> updateAuditStatusPass(@RequestParam Long articleId,
                                                @Parameter(description = "检查ID列表，逗号分隔") @RequestParam List<Long> resultIds) {
        Boolean success = checkResultService.updateAuditStatus(articleId, resultIds, AuditStatusEnum.PASS);
        return success ? Result.ok("操作成功") : Result.error("操作失败");
    }

    @Operation(summary = "错词审核-撤回")
    @PostMapping("audit-withdraw")
    public Result<String> updateAuditStatusWithdraw(@RequestParam Long articleId,
                                                    @Parameter(description = "检查ID列表，逗号分隔") @RequestParam List<Long> resultIds) {
        Boolean success = checkResultService.updateAuditStatus(articleId, resultIds, AuditStatusEnum.WAITING_FOR_REVIEW);
        return success ? Result.ok("操作成功") : Result.error("操作失败");
    }


}