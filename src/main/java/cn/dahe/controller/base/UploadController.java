package cn.dahe.controller.base;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.URLUtil;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 上传
 */
@RestController
@RequestMapping("/pro/upload")
@Slf4j
@Tag(name = "上传文件")
public class UploadController {
    
    // @Resource
    // private AttachmentService attachmentService;
    //
    // /**
    //  * 上传文件
    //  *
    //  * @param file
    //  * @return
    //  */
    // @PostMapping("file")
    // @OperateLog(name = "上传文件", type = OperateTypeEnum.UPDATE)
    // public Result uploadFile(MultipartFile file) {
    //     if (file == null) {
    //         return Result.error("请选择要上传的附件");
    //     }
    //     // 文件存储
    //     Map<String, String> map = UploadWsUtil.uploadFileMap(file);
    //     // 七牛上传成功后，保存到附件表
    //     if (StringUtils.isBlank(map.get("url"))) {
    //         return Result.error("附件上传失败");
    //     }
    //     log.info("文件上传成功：【{}】", JSON.toJSONString(map));
    //     Attachment attachment = new Attachment();
    //     attachment.setCreateTime(new Date());
    //     attachment.setName(map.get("filePath"));
    //     attachment.setAlias(map.get("alias"));
    //     attachment.setSuffix(map.get("suffix"));
    //     attachment.setSize(file.getSize());
    //     attachment.setUrl(map.get("url"));
    //     ArrayList<String> list = new ArrayList<>();
    //     list.add("xls");
    //     list.add("xlsx");
    //     String privewUrl = generatePreviewUrl(attachment.getUrl());
    //     if (list.contains(attachment.getSuffix())) {
    //         privewUrl = privewUrl + "&officePreviewType=html";
    //     }
    //     attachment.setPrivewUrl(privewUrl);
    //     if (!attachmentService.save(attachment)) {
    //         return Result.error("附件上传失败");
    //     }
    //     return Result.ok(attachment);
    // }


    public static String generatePreviewUrl(String originalUrl) {
        if (StringUtils.isBlank(originalUrl)) {
            return "";
        }

        String encodedUrl = Base64.encode(originalUrl);
        String urlEncodedUrl = URLUtil.encode(encodedUrl);

        return "https://fileview.dahe.cn/onlinePreview?url=" + urlEncodedUrl;
    }


}
