package cn.dahe.model.message;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 文章消息模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ArticleMessage implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 文章ID
     */
    private String articleId;

    /**
     * 文章URL
     */
    private String articleUrl;

    /**
     * 文章来源
     */
    private String source;

    /**
     * 文章标题
     */
    private String title;

    /**
     * 文章正文
     */
    private String content;

    /**
     * 文章源码
     */
    private String sourceCode;

    /**
     * 消息创建时间
     */
    private LocalDateTime createTime;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 消息ID（Stream ID）
     */
    private String messageId;

    /**
     * 其他元数据（JSON格式）
     */
    private String metadata;
}