package cn.dahe.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 站点转载信源VO
 */
@Data
@Schema(name = "站点转载信源VO")
public class WebsiteReprintSourceVO {

    /**
     * 主键ID
     */
    @Schema(description = "转载信源id")
    private Long id;

    /**
     * 站点ID
     */
    @Schema(description = "站点ID")
    private Long websiteId;

    /**
     * 站点名称
     */
    @Schema(description = "站点名称")
    private String websiteName;

    /**
     * 转载信源名称
     */
    @Schema(description = "转载信源名称")
    private String reprintSource;

    /**
     * 是否过滤
     */
    @Schema(description = "过滤状态 0未过滤 1已过滤")
    private Integer filterStatus;

    /**
     * 过滤时间
     */
    @Schema(description = "过滤时间")
    private Date filterTime;

}
