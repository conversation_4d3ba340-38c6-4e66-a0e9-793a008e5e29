package cn.dahe.model.vo;

import cn.dahe.service.WebsiteGroupService;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 网站文章错误统计VO
 */
@Data
@Schema(description = "网站文章错误统计")
public class WebsiteArticleCheckStatsVO {

    /**
     * 网站ID
     */
    @Schema(description = "网站ID")
    private Long websiteId;

    @Schema(description = "网站名称")
    private String websiteName;

    @Schema(description = "网站URL")
    private String websiteUrl;

    /**
     * 分组ID
     */
    @Schema(description = "分组ID")
    private Integer groupId;

    /**
     * 分组名称
     */
    @Schema(description = "分组名称")
    private String groupName;

    public String getGroupName() {
        return WebsiteGroupService.getGroupName(this.groupId);
    }


    /**
     * 文章总数
     */
    @Schema(description = "文章总数")
    private Integer articleCount;

    /**
     * 文章总字数
     */
    @Schema(description = "文章字数")
    private Integer articleLength;

    /**
     * 错误总数
     */
    @Schema(description = "检测错误")
    private Integer checkResultCount;

    /**
     * 一级错误数量
     */
    @Schema(description = "一级错误数量")
    private Integer lv1CheckResultCount;

    /**
     * 二级错误数量
     */
    @Schema(description = "二级错误数量")
    private Integer lv2CheckResultCount;

    /**
     * 三级错误数量
     */
    @Schema(description = "三级错误数量")
    private Integer lv3CheckResultCount;

    /**
     * 四级错误数量
     */
    @Schema(description = "四级错误数量")
    private Integer lv4CheckResultCount;

    /**
     * 五级错误数量
     */
    @Schema(description = "五级错误数量")
    private Integer lv5CheckResultCount;

    /**
     * TODO 尚未赋值
     */
    @Schema(description = "最新发布时间（尚未赋值）")
    private Date latestPubTime;
} 