package cn.dahe.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 附件检查VO - 对应原型图字段
 *
 * <AUTHOR>
 * @date 2025-07-30
 */

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(name = "附件检查视图对象", description = "网站附件检查信息视图")
public class AttachmentCheckVO extends CheckVO{

    @Schema(description = "附件编号", example = "1")
    private Long id;

    @Schema(description = "来源网站", example = "河南师范大学")
    private String websiteName;

    @Schema(description = "来源网站网址", example = "http://www.hfnu.edu.cn")
    private String websiteUrl;

    @Schema(description = "来源页面标题")
    private String sourceName;

    @Schema(description = "来源页面网址")
    private String sourceUrl;

    @Schema(description = "发布时间", example = "2025-07-30 10:30:00")
    private Date pubTime;

    @Schema(description = "检测时间", example = "2025-07-30 10:30:00")
    private Date checkTime;


    // ==================== 扩展字段 ====================

    @Schema(description = "附件名称", example = "政策文件.pdf")
    private String attachmentName;

    @Schema(description = "附件地址", example = "https://www.example.gov.cn/files/attach.pdf")
    private String attachmentUrl;

    @Schema(description = "附件类型", example = "PDF")
    private String attachmentType;

    @Schema(description = "附件大小", example = "1.5MB")
    private String attachmentSize;

}
