package cn.dahe.model.vo;

import cn.dahe.service.CheckErrorLevelService;
import cn.dahe.service.CheckErrorTypeService;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 文章错误信息VO
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
@Schema(name = "内容检查的错误信息")
public class CheckResultVO {

    @Schema(description = "检查ID", example = "1")
    private Long resultId;

    @Schema(description = "错误词", example = "中国人民共和国")
    private String errorWord;

    @Schema(description = "建议正确用词", example = "中华人民共和国")
    private String suggestWord;

    @Schema(description = "错误类型，对应二级或三级", example = "1")
    private Long errorType;

    @Schema(description = "错误类型名称（二级类型名称，如果有三级类型则显示'二级名称-三级名称'）", example = "错别字-常见错别字")
    private String errorTypeName;

    @Schema(description = "错误等级", example = "1")
    private Integer errorLevel;

    @Schema(description = "错误等级名称", example = "严重")
    private String errorLevelName;

    @Schema(description = "在文本中的位置（相对于纯内容）", example = "100")
    private Integer position;

    @Schema(description = "html错误词", example = "中国人民共和国")
    private String htmlErrorWord;

    @Schema(description = "在文本中的位置（相对于HTML内容）", example = "100")
    private Integer htmlPosition;

    @Schema(description = "错误出现位置（0-正文，1-标题）", example = "0")
    private Integer articleLocation;

    @Schema(description = "审核状态（0未审核 1审核通过 2审核驳回）", example = "0")
    private Integer auditStatus;

    @Schema(description = "是否过滤（0-未过滤 1-已过滤）", example = "0")
    private Integer filterStatus;

    @Schema(description = "所在上下文（包含错误词的句子，超长时截取到合适的标点符号），纯文本", example = "中国是一个伟大的国家")
    private String context;
    @Schema(description = "已标记所在上下文", example = "中国是一个<span class=\"error-word\">伟大</span>的<span class=\"error-word\">国家</span>")
    private String markedContext;

    public String getErrorTypeName() {
        return CheckErrorTypeService.getTypeName(errorType);
    }

    public String getErrorLevelName() {
        return CheckErrorLevelService.getLevelName(errorLevel);
    }
} 