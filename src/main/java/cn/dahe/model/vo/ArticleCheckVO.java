package cn.dahe.model.vo;

import cn.dahe.service.WebsiteGroupService;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 文章检查结果VO
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "文章检查结果")
public class ArticleCheckVO extends CheckVO {
    /**
     * 文章ID
     */
    @Schema(description = "文章ID")
    private Long articleId;

    @Schema(description = "网站ID")
    public Long websiteId;

    @Schema(description = "分组ID")
    private Integer groupId;

    @Schema(description = "分组名称")
    private String groupName;

    public String getGroupName() {
        return WebsiteGroupService.getGroupName(this.groupId);
    }


    @Schema(description = "文章审核状态：0未审核 1审核通过 2审核驳回", example = "0")
    private Integer auditStatus;
    /**
     * 发布时间
     */
    @Schema(description = "发布时间")
    private Date pubTime;

    @Schema(description = "成文时间")
    private Date writeTime;

    @Schema(description = "作者")
    private String author;

    @Schema(description = "网址")
    private String url;

    @Schema(description = "快照网址")
    private String snapshotUrl;

    @Schema(description = "转载信源")
    private String reprintSource;

    @Schema(description = "网站名称")
    private String websiteName;

    @Schema(description = "检查策略 0 查全 1 查准")
    private Integer checkStrategy;
    /**
     * 检查时间
     */
    @Schema(description = "检测时间")
    private Date checkTime;


    @Schema(description = "检测字数")
    private Integer articleLength;

    public Integer getArticleLength() {
        return StrUtil.length(this.getCleanedContent());
    }


}