package cn.dahe.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 网站文章错误统计总计VO
 */
@Data
@Schema(description = "网站文章错误统计总计")
public class WebsiteArticleCheckTotalStatsVO {

    /**
     * 文章总数
     */
    @Schema(description = "文章总数")
    private Integer articleCount;

    /**
     * 文章总字数
     */
    @Schema(description = "文章总字数")
    private Integer articleLength;

    /**
     * 网站总数
     */
    @Schema(description = "网站总数")
    private Integer websiteCount;

    /**
     * 错误总数
     */
    @Schema(description = "错误总数")
    private Integer checkResultCount;

    /**
     * 一级错误网站数量
     */
    @Schema(description = "一级错误网站数量")
    private Integer lv1WebsiteCount;

    /**
     * 二级错误网站数量
     */
    @Schema(description = "二级错误网站数量")
    private Integer lv2WebsiteCount;

    /**
     * 三级错误网站数量
     */
    @Schema(description = "三级错误网站数量")
    private Integer lv3WebsiteCount;

    /**
     * 四级错误网站数量
     */
    @Schema(description = "四级错误网站数量")
    private Integer lv4WebsiteCount;

    /**
     * 五级错误网站数量
     */
    @Schema(description = "五级错误网站数量")
    private Integer lv5WebsiteCount;
} 