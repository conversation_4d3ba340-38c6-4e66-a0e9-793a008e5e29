package cn.dahe.model.query;

import cn.dahe.model.dto.Query;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 首页更新主表查询参数
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "首页更新主表查询参数")
public class ChkUpdateSiteIndexQuery extends Query {

    @Schema(description = "分组id", example = "1,2,3")
    private String groupId = "";

    @Schema(description = "分组名称", example = "站1,站2,站3")
    private String groupName = "";

    @Schema(description = "网站id", example = "1,2,3")
    private String websiteId = "";

    @Schema(description = "网站名称", example = "名称1,名称2,名称3")
    private String websiteName = "";

    @JsonIgnore
    @Schema(description = "网站首页地址", example = "https://www.a1.cn,https://www.a2.cn,https://www.a3.cn")
    private String websiteIndexUrl = "";

    @Schema(description = "解析开始时间", example = "2025-07-01 00:00:00")
    private String beginTime = "";

    @Schema(description = "解析结束时间", example = "2025-07-31 23:59:59")
    private String endTime = "";
}
