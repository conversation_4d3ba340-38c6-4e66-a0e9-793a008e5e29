package cn.dahe.model.query;

import cn.dahe.enums.ArticleSortTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 文章检查查询参数
 *
 * <AUTHOR>
 * @date 2025-07-10
 */

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "文章检查查询参数")
public class ArticleCheckQuery extends CheckResultQuery{

    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码最小值为 1")
    @Schema(description = "页码", example = "1", type = "integer", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer page = 1;

    @NotNull(message = "每页条数不能为空")
    @Min(value = 1, message = "每页条数最小值为 1")
    @Schema(description = "每页大小", example = "10", type = "integer")
    private Integer limit = 10;

    @Schema(description = "巡查精准度 0 查全 1 查准", example = "0,1", type = "array", implementation = Integer.class)
    private List<Integer> checkStrategies;

    @Schema(description = "关键词，匹配文章标题", type = "string")
    private String keyword;

    @Schema(description = "转载信源搜索", type = "string")
    private String reprintSource;


    @Schema(description = "文章审核状态", example = "0,1,2", type = "array", implementation = Integer.class)
    private List<Integer> articleAuditStatuses;


    @Schema(description = "网站ID列表", example = "6,7", type = "array", implementation = Long.class)
    private List<Long> websiteIds;

    @Schema(description = "发布时间起始时间", type = "string", format = "date-time")
    private Date pubBeginTime;

    @Schema(description = "发布时间结束时间", type = "string", format = "date-time")
    private Date pubEndTime;

    @Schema(description = "检测时间起始时间", type = "string", format = "date-time")
    private Date checkStartTime;

    @Schema(description = "检测时间结束时间", type = "string", format = "date-time")
    private Date checkEndTime;

    @Schema(description = "排序列（ 0 发布时间 1 检测时间 ）", example = "0", type = "integer")
    private Integer sortField = 0;

    @Schema(description = "排序顺序（0:降序 1:升序）", example = "0", type = "integer")
    private Integer sortDirection = 0;

    /**
     * 不要删除，sql排序用
     */
    public ArticleSortTypeEnum getSortTypeEnum() {
        return ArticleSortTypeEnum.getByFieldAndDirection(sortField, sortDirection);
    }
} 