package cn.dahe.model.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 站点转载信源查询对象
 */
@Data
@Schema(description = "站点转载信源查询对象")
public class WebsiteReprintSourceQuery {

    @Schema(description = "页码", example = "1", type = "integer")
    private Integer page = 1;

    @Schema(description = "每页大小", example = "10", type = "integer")
    private Integer limit = 10;
    /**
     * 站点ID集合
     */
    @Schema(description = "站点ID集合", example = "6,7,8", type = "array", implementation = Long.class)
    private List<Long> websiteIds;

    /**
     * 是否过滤
     */
    @Schema(description = "过滤状态集合", example = "0,1", type = "array", implementation = Integer.class)
    private List<Integer> filterStatuses;

    /**
     * 转载信源名称
     */
    @Schema(description = "转载信源关键字搜索", example = "百度")
    private String reprintSource;

    /**
     * 过滤时间范围-开始
     */
    @Schema(description = "过滤时间范围-开始", example = "2023-01-01", format = "date")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date filterBeginDate;

    /**
     * 过滤时间范围-结束
     */
    @Schema(description = "过滤时间范围-结束", example = "2023-01-01", format = "date")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date filterEndDate;

}
