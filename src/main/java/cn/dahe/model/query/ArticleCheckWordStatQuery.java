package cn.dahe.model.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-07-05
 */
@Data
public class ArticleCheckWordStatQuery {

    Integer page = 1;

    Integer limit = 10;

    @Schema(description = "检查类型 0 网站 1 网站附件 2 微博 3 微信公众号 4 新媒体其他平台 暂时不需要传")
    private Integer checkType;

    @Schema(description = "关键词，匹配正词和错词", type = "string")
    private String keyword;

    @Schema(description = "网站ID列表", example = "6,7", type = "array", implementation = Long.class)
    private List<Long> websiteIds;

    @Schema(description = "错误等级", example = "1,2,3,4,5", type = "array", implementation = Integer.class)
    private List<Integer> errorLevels;

    /**
     * 发布开始时间
     */
    @Schema(description = "发布开始时间", example = "2025-07-16")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date pubBeginDate;
    /**
     * 发布结束时间
     */
    @Schema(description = "发布结束时间", example = "2025-07-17")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date pubEndDate;

    @Schema(description = "一级错误类型", example = "1,2,3", type = "array", implementation = Long.class)
    private List<Long> firstErrorTypes;

    @Schema(description = "二级错误类型", type = "array", implementation = Long.class)
    private List<Long> secondErrorTypes;

    @Schema(description = "三级错误类型", type = "array", implementation = Long.class)
    private List<Long> thirdErrorTypes;

    @Schema(description = "勘误过滤词", example = "0,1", type = "array", implementation = Integer.class)
    private List<Integer> filterStatuses;

}
