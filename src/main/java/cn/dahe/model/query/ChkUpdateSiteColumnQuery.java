package cn.dahe.model.query;

import cn.dahe.model.dto.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 栏目更新检查查询参数 - 对应原型图功能
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "栏目更新检查查询参数")
public class ChkUpdateSiteColumnQuery extends Query {

    // ==================== 原型图对应字段 ====================

    @Schema(description = "栏目名称", example = "新闻动态")
    private String columnName = "";

    @Schema(description = "栏目分类", example = "新闻类")
    private String columnCategory = "";

    @Schema(description = "检查开始时间", example = "2025-07-01 00:00:00")
    private String checkStartTime = "";

    @Schema(description = "检查结束时间", example = "2025-07-31 23:59:59")
    private String checkEndTime = "";

    @Schema(description = "更新开始时间", example = "2025-07-01 00:00:00")
    private String updateStartTime = "";

    @Schema(description = "更新结束时间", example = "2025-07-31 23:59:59")
    private String updateEndTime = "";

    @Schema(description = "网站链接", example = "https://www.example.gov.cn")
    private String websiteUrl = "";

    @Schema(description = "未更新天数", example = "5")
    private Integer notUpdateDays;

    @Schema(description = "检查日期", example = "2025-07-30")
    private String checkDate = "";

    // ==================== 扩展字段 ====================

    @Schema(description = "分组名称", example = "政府网站")
    private String groupName = "";

    @Schema(description = "检测状态", example = "正常")
    private String checkStatus = "";

    @Schema(description = "栏目ID", example = "1")
    private Long columnId;

    // ==================== 兼容旧字段 ====================

    @Schema(description = "解析开始时间(兼容)", example = "2025-07-01 00:00:00")
    private String parseBeginTime = "";

    @Schema(description = "解析结束时间(兼容)", example = "2025-07-31 23:59:59")
    private String parseEndTime = "";

    @Schema(description = "创建开始时间", example = "2025-07-01 00:00:00")
    private String createBeginTime = "";

    @Schema(description = "创建结束时间", example = "2025-07-31 23:59:59")
    private String createEndTime = "";
}
