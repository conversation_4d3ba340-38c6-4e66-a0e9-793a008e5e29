package cn.dahe.model.dto;

import cn.dahe.common.model.ResultCode;
import lombok.Data;

import java.util.Objects;

/**
 * 响应数据
 */
@Data
public class Result<T> {

    private int code;

    private String msg;

    private T data;


    public Result() {
    }

    public Result(int code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    private Result(T data, ResultCode resultCode) {
        this.data = data;
        this.msg = resultCode.getMsg();
        this.code = resultCode.getCode();
    }

    public static Result<String> error(ResultCode resultCode, String msg) {
        return new Result<>(resultCode.getCode(), msg, "");
    }

    public static <T> Result<T> error(ResultCode resultCode, String msg, T data) {
        return new Result<>(resultCode.getCode(), msg, data);
    }

    public static <T> Result<T> ok(int code, String msg, T data) {
        return new Result<>(code, msg, data);
    }

    public static <T> Result<T> error(int code, String msg, T data) {
        return new Result<>(code, msg, data);
    }

    public static <T> Result<T> ok(T data) {
        return new Result<>(data, ResultCode.Success);
    }

    public static Result<String> ok(String msg) {
        return new Result<>(msg, ResultCode.Success);
    }


    public static <T> Result<T> ok() {
        return new Result<>(null, ResultCode.Success);
    }

    public static <T> Result<T> error() {
        return error(ResultCode.Fail);
    }

    public static <T> Result<T> error(String msg) {
        return error(ResultCode.Fail.getCode(), msg);
    }

    public static <T> Result<T> error(ResultCode resultCode) {
        return error(resultCode.getCode(), resultCode.getMsg());
    }

    public static <T> Result<T> error(int code, String msg) {
        Result<T> result = new Result<>();
        result.setCode(code);
        result.setMsg(msg);
        return result;
    }

    public boolean isSuccess() {
        return Objects.equals(ResultCode.Success.getCode(), this.getCode());
    }


}
