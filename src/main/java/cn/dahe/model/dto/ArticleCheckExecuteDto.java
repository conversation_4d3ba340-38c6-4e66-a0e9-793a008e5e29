package cn.dahe.model.dto;

import cn.dahe.entity.CheckTask;
import cn.dahe.entity.CheckContent;
import cn.dahe.utils.HtmlTextMapper;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ArticleCheckExecuteDto {

    private Long articleContentId;

    private CheckTask checkTask;
    private CheckContent checkContent;

    private HtmlTextMapper.HtmlResult titleResult;
    private HtmlTextMapper.HtmlResult contentResult;
}
