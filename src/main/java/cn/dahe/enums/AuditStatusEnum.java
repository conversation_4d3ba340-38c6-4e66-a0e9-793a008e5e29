package cn.dahe.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@Getter
@AllArgsConstructor
public enum AuditStatusEnum {

    WAITING_FOR_REVIEW(0, "待审核"),
    PASS(1, "审核通过"),
    REJECT(2, "审核拒绝");


    public static AuditStatusEnum getEnumByType(Integer type) {
        AuditStatusEnum[] values = AuditStatusEnum.values();
        for (AuditStatusEnum value : values) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }

    private final Integer type;

    private final String desc;

    /**
     * 判断审核状态是否有效
     *
     * @param status 审核状态值
     * @return 是否有效
     */
    public static boolean isValid(Integer status) {
        return status != null && getEnumByType(status) != null;
    }
}
