package cn.dahe.enums;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@Getter
@AllArgsConstructor
public enum WorkAdminReviewStatusEnum {


    WAIT_ADMIN_CHU_PING(1, "等待管理员初评"),


    ENTER_FINAL_REVIEW(2, "进入定评"),

    AWARD_REVIEW(3, "拟获奖");


    private final Integer type;

    private final String desc;
}
