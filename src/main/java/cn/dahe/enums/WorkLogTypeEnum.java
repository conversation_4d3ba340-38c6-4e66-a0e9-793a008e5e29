package cn.dahe.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024-02-26
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@Getter
@AllArgsConstructor
public enum WorkLogTypeEnum {

    DELETE(-1, "作品已被删除"),

    SAVE(1, "作品已被保存"),

    SUBMIT(2, "作品已被提交至待审核"),

    REJECT(3, "作品已被驳回"),

    PASS(4, "作品已被审核通过"),

    UPDATE(5, "作品已被修改"),

    EXPERT_FIRST_UPDATE_REVIEW_RESULT(6, "专家-初评-更新评审结果"),
    EXPERT_FIRST_SUBMIT_REVIEW_RESULT(7, "专家-初评-提交评审结果"),
    EXPERT_FINAL_UPDATE_REVIEW_RESULT(8, "专家-定评-更新评审结果"),
    EXPERT_FINAL_SUBMIT_REVIEW_RESULT(9, "专家-定评-提交评审结果"),

    ADMIN_ENTER_FINAL_REVIEW(10, "管理员-进入定评"),
    ADMIN_CANCEL_ENTER_FINAL_REVIEW(11, "管理员-取消进入定评"),
    ADMIN_PROPOSED_FOR_AWARD(12, "管理员-拟获奖"),
    ADMIN_CANCEL_PROPOSED_FOR_AWARD(13, "管理员-取消拟获奖");


    public static WorkLogTypeEnum getEnumByType(int type) {
        WorkLogTypeEnum[] values = WorkLogTypeEnum.values();
        for (WorkLogTypeEnum value : values) {
            if (value.getType() == type) {
                return value;
            }
        }
        return null;
    }


    private final Integer type;

    private final String desc;
}
