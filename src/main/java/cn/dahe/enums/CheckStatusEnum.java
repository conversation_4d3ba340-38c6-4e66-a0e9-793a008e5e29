package cn.dahe.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文章检查状态枚举
 *
 * <AUTHOR>
 * @date 2024-01-24
 */
@Getter
@AllArgsConstructor
@Schema(description = "文章检查状态枚举")
public enum CheckStatusEnum {

    /**
     * 未检查
     */
    UNCHECKED(0, "未检查"),

    /**
     * 已检查
     */
    CHECKED(1, "已检查"),

    /**
     * 检查失败待重试
     */
    FAILED_RETRY(2, "检查失败待重试"),

    /**
     * 检查失败
     */
    FAILED(3, "检查失败");

    @JsonValue
    private final Integer code;
    private final String desc;

    /**
     * 根据code获取枚举
     *
     * @param code 状态码
     * @return 对应的枚举值，如果没找到返回null
     */
    public static CheckStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CheckStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}