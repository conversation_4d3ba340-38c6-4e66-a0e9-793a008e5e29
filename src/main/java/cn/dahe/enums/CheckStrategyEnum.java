package cn.dahe.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 检查策略枚举
 */
@Getter
@AllArgsConstructor
public enum CheckStrategyEnum {

    RECALL(0, "查全"),
    PRECISION(1, "查准");

    private final int code;
    private final String desc;

    public static String getDesc(Integer code) {
        CheckStrategyEnum enumByType = getEnumByType(code);
        if (enumByType == null) {
            return null;
        }
        return enumByType.getDesc();
    }

    public static CheckStrategyEnum getEnumByType(Integer type) {
        if (type == null) {
            return null;
        }
        for (CheckStrategyEnum value : CheckStrategyEnum.values()) {
            if (value.getCode() == type) {
                return value;
            }
        }
        return null;
    }

    /**
     * 判断审核状态是否有效
     *
     * @param status 审核状态值
     * @return 是否有效
     */
    public static boolean isValid(Integer status) {
        return getEnumByType(status) != null;
    }
} 