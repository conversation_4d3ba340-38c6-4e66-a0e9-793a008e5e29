package cn.dahe.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文章检查类型枚举
 *
 * <AUTHOR>
 * @date 2024-01-24
 */
@Getter
@AllArgsConstructor
@Schema(description = "文章检查类型枚举")
public enum CheckTypeEnum {

    /**
     * 网站
     */
    WEBSITE(0, "网站"),

    /**
     * 网站附件
     */
    WEBSITE_ATTACHMENT(1, "网站附件"),

    /**
     * 微博
     */
    WEIBO(2, "微博"),

    /**
     * 微信公众号
     */
    WECHAT(3, "微信公众号"),

    /**
     * 新媒体其他平台
     */
    OTHER_MEDIA(4, "新媒体其他平台");

    @JsonValue
    private final Integer code;
    private final String desc;

    /**
     * 根据code获取枚举
     *
     * @param code 类型码
     * @return 对应的枚举值，如果没找到返回null
     */
    public static CheckTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CheckTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}