package cn.dahe.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@Getter
@AllArgsConstructor
public enum WorkExpertReviewStatusEnum {


    WAIT_REVIEW(1, "待评审"),

    ALREADY_REVIEW(2, "已评审"),

    SUBMITTED(3, "已提交");


    private final Integer type;

    private final String desc;



}
