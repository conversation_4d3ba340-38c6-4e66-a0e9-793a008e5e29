package cn.dahe.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;


@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@Getter
@AllArgsConstructor
public enum WorkAwardLevelEnum {

    WEI_HUO_JIANG(0, "不获奖"),

    TE_DENG_JINAG(1, "特等奖"),

    YI_DENG_JINAG(2, "一等奖"),

    ER_DENG_JINAG(3, "二等奖"),

    SAN_DENG_JINAG(4, "三等奖"),

    JIN_RU_DING_PING(5,"进入定评");


    private final Integer type;

    private final String desc;


    public static WorkAwardLevelEnum getEnumByType(Integer type) {
        WorkAwardLevelEnum[] values = WorkAwardLevelEnum.values();
        for (WorkAwardLevelEnum value : values) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }
}
