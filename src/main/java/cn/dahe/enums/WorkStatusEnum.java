package cn.dahe.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@Getter
@AllArgsConstructor
public enum WorkStatusEnum {

    DELETE(-1, "删除"),

    WAIT_SUBMIT(1, "待提交"),

    WAIT_AUDIT(2, "待审核"),

    WAIT_EDIT(3, "待修改"),

    PASS(4, "审核通过");


    public static WorkStatusEnum getEnumByType(Integer type) {
        WorkStatusEnum[] values = WorkStatusEnum.values();
        for (WorkStatusEnum value : values) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }

    private final Integer type;

    private final String desc;


}
