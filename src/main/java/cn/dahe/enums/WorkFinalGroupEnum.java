package cn.dahe.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024-04-18
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@Getter
@AllArgsConstructor
public enum WorkFinalGroupEnum {


//    1党报及子报组
//    2广播电视组
//    3新媒体组
//    4新闻业务研究组（报刊、县级融媒部分）
//    5新闻业务研究组（广电部分）
//    6行业报刊组
//    7中央驻豫组（报纸部分）
//    8中央驻豫组（广电、新媒体部分）



}
