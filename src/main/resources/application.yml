server:
  port: 8081
  servlet:
    session:
      timeout: 3600s
      cookie:
        name: sid
    context-path: /platform-api

spring:
  profiles:
    active: dev
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
    format:
      # 默认接参日期格式 不一样的请在参数上覆盖
      date: yyyy-MM-dd
      date-time: yyyy-MM-dd HH:mm:ss
      time: HH:mm:ss
  jackson:
    # 全局配置 java.util.Date 类型的日期格式
    date-format: yyyy-MM-dd HH:mm:ss
    # 设置时区（重要，避免时区偏移）
    time-zone: GMT+8
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

# springdoc-openapi项目配置
springdoc:
  default-flat-param-object: true
  group-configs:
    - group: '登录及基础'
      packages-to-scan: cn.dahe.controller.base
    - group: '检查配置'
      packages-to-scan: cn.dahe.controller.check
    - group: '网站检查'
      packages-to-scan: cn.dahe.controller.article
    - group: '附件检查'
      packages-to-scan: cn.dahe.controller.attachment
    - group: '更新检查'
      packages-to-scan: cn.dahe.controller
      paths-to-match:
        - /pro/chk-update-site-column/**
        - /pro/chk-update-site-index/**
        - /pro/web/access-record/**
    - group: '链接检查'
      packages-to-scan: cn.dahe.controller
      paths-to-match:
        - /pro/web/dead-link/**
        - /pro/web/out-link/**
    - group: '网站管理'
      packages-to-scan: cn.dahe.controller.website
      paths-to-match:
        - /pro/channel/**
        - /pro/website-group/**
        - /pro/web-site/**
    - group: '系统管理'
      packages-to-scan: cn.dahe.controller.system
# knife4j的增强配置
knife4j:
  enable: true
  setting:
    language: zh_cn
    # 开启动态请求参数
    enable-dynamic-parameter: true

async:
  executor:
    thread:
      core_pool_size: 5
      max_pool_size: 5
      queue_capacity: 9999
      name:
        prefix: website-async-service

mybatis:
  table:
    # create  系统启动后，会将所有的表删除掉，然后根据model中配置的结构重新建表，该操作会破坏原有数据。
    # update  系统会自动判断哪些表是新建的，哪些字段要修改类型等，哪些字段要删除，哪些字段要新增，该操作不会破坏原有数据。
    # none 	  系统不做任何处理。
    # add	  新增表/新增字段/新增索引/新增唯一约束的功能，不做做修改和删除 (只在版本1.0.9.RELEASE及以上支持)。
    auto: update
  model:
    # 扫描用于创建表的对象的包名，多个包用","隔开
    pack: cn.dahe.entity
  database:
    # 数据库类型 目前只支持mysql
    type: mysql



mybatis-plus:
  mapper-locations: classpath*:mapper/*.xml,classpath*:com/gitee/sunchenbin/mybatis/actable/mapping/*/*.xml
  global-config:
    db-config:
      # 全局默认主键类型
      id-type: auto
      # 表名前缀
      table-prefix: t_
      # 表名是否使用驼峰转下划线命名,只对表名生效
      table-underline: true
      # logic-delete-field: is_del # 全局逻辑删除字段名
      # logic-delete-value: 1
      # logic-not-delete-value: 0
  configuration:
    # 是否开启自动驼峰命名规则（camel case）映射，默认开启
    # 即从经典数据库列名 A_COLUMN（下划线命名） 到经典 Java 属性名 aColumn（驼峰命名） 的类似映射
    map-underscore-to-camel-case: true
    # 指定当结果集中值为 null 的时候是否调用映射对象的 Setter（Map 对象时为 put）方法，
    # 通常运用于有 Map.keySet() 依赖或 null 值初始化的情况
    # 通俗的讲，即 MyBatis 在使用 resultMap 来映射查询结果中的列，如果查询结果中包含空值的列，则 MyBatis 在映射的时候，不会映射这个字段，
    # 这就导致在调用到该字段的时候由于没有映射，取不到而报空指针异常
    callSettersOnNulls: false
  #    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  # 通过该属性可以给包中的类注册别名，注册后在 Mapper 对应的 XML 文件中可以直接使用类名，而不用使用全限定的类名
  type-aliases-package: cn.dahe.model.entity

# 内容检查配置
content-check:
  api:
    url: http://localhost:8081/platform-api/sim/check/check
    timeout: 30000