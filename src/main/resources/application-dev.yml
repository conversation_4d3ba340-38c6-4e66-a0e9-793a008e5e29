spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: jdbc:mysql://**********:3306/axxm?serverTimezone=GMT%2b8&useUnicode=true&characterEncoding=utf8
      username: root
      password: 123zgf
      #      filters: stat,wall
      filters: stat
      max-active: 100
      initial-size: 10
      max-wait: 60000
      min-idle: 1
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: select '1'
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-open-prepared-statements: 50
      max-pool-prepared-statement-per-connection-size: 20

  redis:
    host: **********
    port: 63795
    password: 123zgf


content-check:
  api:
    url: https://dhax-beta.dahe.cn/platform-api/sim/check/check
    timeout: 30000


