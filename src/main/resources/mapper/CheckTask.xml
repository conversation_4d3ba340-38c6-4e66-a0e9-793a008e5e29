<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dahe.dao.CheckTaskDao">


    <sql id="checkStrategyCondition">
        <choose>
            <when test="query.checkStrategies != null and query.checkStrategies.size() > 0">
                AND tct.check_strategy IN
                <foreach collection="query.checkStrategies" item="strategy" open="(" separator="," close=")">
                    #{strategy}
                </foreach>
            </when>
        </choose>
    </sql>

    <sql id="checkTimeCondition">
        <if test="query.checkStartTime != null">
            AND tct.check_time >= #{query.checkStartTime}
        </if>
        <if test="query.checkEndTime != null">
            AND tct.check_time &lt;= #{query.checkEndTime}
        </if>
    </sql>

    <sql id="checkTypeCondition">
        <if test="query.checkType != null">
            AND tct.check_type >= #{query.checkType}
        </if>
    </sql>




</mapper>