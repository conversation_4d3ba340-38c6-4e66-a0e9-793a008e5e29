<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.dahe.dao.AttachmentDao">

    <sql id="attachmentCheckVOColumn">
        tat.id,
        tw.web_name as website_name,
        tw.web_url as website_url,
        ta.title as source_name,
        ta.article_url as source_url,
        ta.pub_time,
        tct.check_time,
        tat.attachment_name,
        tat.attachment_url,
        tat.attachment_type,
        tat.attachment_size
    </sql>

    <!-- ==================== 附件检查记录 ==================== -->

    <select id="pageAttachmentInfo" resultType="cn.dahe.model.vo.AttachmentCheckVO">
        SELECT
        <include refid="attachmentCheckVOColumn"/>
        FROM t_attachment tat
        left join t_check_task tct on tat.check_id = tct.id
        left join t_article ta on tat.parent_article_id = ta.id
        LEFT JOIN t_website tw ON tat.website_id = tw.id
        <where>
            exists (
            SELECT 1
            FROM t_check_result tcr
            WHERE tat.check_id = tcr.check_id
            )
            <include refid="cn.dahe.dao.WebsiteDao.websiteCondition"/>
            <include refid="cn.dahe.dao.CheckTaskDao.checkStrategyCondition"/>
            <include refid="cn.dahe.dao.ArticleDao.pubDateCondition"/>
        </where>
        <!-- 排序 -->
        ORDER BY ${query.sortTypeEnum.field} ${query.sortTypeEnum.direction}
    </select>

    <select id="getAttachmentInfo" resultType="cn.dahe.model.vo.AttachmentCheckVO">
        SELECT
        <include refid="attachmentCheckVOColumn"/>
        FROM t_attachment tat
        left join t_check_task tct on tat.check_id = tct.id
        left join t_article ta on tat.parent_article_id = ta.id
        LEFT JOIN t_website tw ON tat.website_id = tw.id
        WHERE tat.id = #{id}
    </select>



</mapper>
