<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dahe.dao.ArticleDao">


    <sql id="articleAuditCondition">
        <if test="query.articleAuditStatuses != null and query.articleAuditStatuses.size() > 0">
            AND ta.audit_status IN
            <foreach collection="query.articleAuditStatuses" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
    </sql>
    <sql id="disposalStatusCondition">
        <if test="query.disposalStatuses != null and query.disposalStatuses.size() > 0">
            AND ta.disposal_status IN
            <foreach collection="query.disposalStatuses" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
    </sql>
    <sql id="rectifyStatusCondition">
        <if test="query.rectifyStatuses != null and query.rectifyStatuses.size() > 0">
            AND ta.rectify_status IN
            <foreach collection="query.rectifyStatuses" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
    </sql>
    <sql id="pubTimeCondition">
        <if test="query.pubBeginTime != null">
            AND ta.pub_time >= #{query.pubBeginTime}
        </if>
        <if test="query.pubEndTime != null">
            AND ta.pub_time &lt;= #{query.pubEndTime}
        </if>
    </sql>
    <sql id="pubDateCondition">
        <if test="query.pubBeginDate != null">
            AND ta.pub_time >= DATE_FORMAT(#{query.pubBeginDate}, '%Y-%m-%d 00:00:00')
        </if>
        <if test="query.pubEndDate != null">
            AND ta.pub_time &lt;= DATE_FORMAT(#{query.pubEndDate}, '%Y-%m-%d 23:59:59')
        </if>
    </sql>


    <sql id="articleCheckVOColumn">
        tct.id as taskId,
        ta.id as articleId,
        ta.author,
        ta.pub_time,
        ta.write_time,
        tw.group_id,
        tw.id as website_id,
        tw.web_name as website_name,
        ta.article_url as url,
        tcc.cleaned_title,
        tcc.cleaned_content,
        tcc.compressed_title as htmlTitle,
        tcc.compressed_content as htmlContent,
        ta.audit_status,
        tct.check_time,
        tct.check_strategy,
        twrs.reprint_source
    </sql>

    <!-- 分页查询文章检查结果 -->
    <select id="pageArticleInfo" resultType="cn.dahe.model.vo.ArticleCheckVO">
        SELECT
        <include refid="articleCheckVOColumn"/>
        FROM t_article ta
        left join t_website tw on ta.website_id = tw.id
        left join t_check_task tct on ta.check_id = tct.id
        LEFT JOIN t_check_content tcc ON tct.id = tcc.id
        left join t_website_reprint_source twrs on twrs.id = ta.reprint_source_id
        <where>
            tct.check_status = 1
            and (ta.reprint_source_id is null or twrs.filter_status = 0)
            <include refid="cn.dahe.dao.WebsiteReprintSourceDao.reprintSourceCondition"/>
            and
            exists (
            SELECT 1
            FROM t_check_result tcr
            left join t_check_word tcw on tcr.word_id = tcw.id
            WHERE ta.check_id = tcr.check_id
            <include refid="cn.dahe.dao.CheckWordDao.errorTypeAndLevelCondition"/>
            <include refid="cn.dahe.dao.CheckResultDao.resultAuditCondition"/>
            <include refid="cn.dahe.dao.CheckWordDao.filterStatusCondition"/>
            <include refid="cn.dahe.dao.CheckWordDao.wordCondition"/>
            <include refid="cn.dahe.dao.CheckResultDao.wordIdCondition"/>
            )
            <include refid="cn.dahe.dao.CheckTaskDao.checkStrategyCondition"/>
            <include refid="articleAuditCondition"/>
            <include refid="cn.dahe.dao.WebsiteDao.websiteCondition"/>
            <if test="query.keyword != null and query.keyword != ''">
                AND (
                tcc.cleaned_title LIKE CONCAT('%', #{query.keyword}, '%')
                or tcc.cleaned_content LIKE CONCAT('%',#{query.keyword}, '%')
                )
            </if>
            <include refid="pubTimeCondition"/>
            <include refid="cn.dahe.dao.CheckTaskDao.checkTimeCondition"/>
        </where>
        ORDER BY ${query.sortTypeEnum.field} ${query.sortTypeEnum.direction}
    </select>

    <select id="getArticleInfo" resultType="cn.dahe.model.vo.ArticleCheckVO">
        SELECT
        <include refid="articleCheckVOColumn"/>
        FROM t_article ta
        left join t_website tw on ta.website_id = tw.id
        left join t_check_task tct on ta.check_id = tct.id
        LEFT JOIN t_check_content tcc ON tct.id = tcc.id
        left join t_website_reprint_source twrs on twrs.id = ta.reprint_source_id
        where ta.id = #{articleId}
    </select>


</mapper>