<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.dahe.dao.WebsiteGroupDao">

    <resultMap id="WebsiteGroupVOMap" type="cn.dahe.model.vo.WebsiteGroupVO">
        <id property="id" column="group_id"/>
        <result property="groupName" column="group_name"/>
        <collection property="websites" ofType="cn.dahe.model.vo.WebsiteVO">
            <id property="id" column="website_id"/>
            <result property="webName" column="web_name"/>
            <result property="webUrl" column="web_url"/>
            <result property="webCodeId" column="web_code_id"/>
            <result property="groupId" column="website_group_id"/>
            <result property="checkStrategy" column="check_strategy"/>
        </collection>
    </resultMap>

    <select id="listWithWebsites" resultMap="WebsiteGroupVOMap">
        -- 先查询所有未分组的网站
        SELECT 
            0 as group_id,
            '未分组' as group_name,
            w.id as website_id,
            w.web_name,
            w.web_url,
            w.web_code_id,
            w.group_id as website_group_id,
            w.check_strategy
        FROM t_website w
        left join t_website_group wg on wg.id = w.group_id and wg.is_del = 0 and wg.status = 1
        WHERE w.status = 1 AND w.is_del = 0
        AND (wg.id is null)
        
        UNION ALL
        
        -- 再查询所有分组及其网站
        SELECT 
            g.id as group_id,
            g.group_name,
            w.id as website_id,
            w.web_name,
            w.web_url,
            w.web_code_id,
            w.group_id as website_group_id,
            w.check_strategy
        FROM t_website_group g
        LEFT JOIN t_website w ON g.id = w.group_id AND w.status = 1 AND w.is_del = 0
        WHERE g.status = 1 
        AND g.is_del = 0
        ORDER BY group_id
    </select>

</mapper> 