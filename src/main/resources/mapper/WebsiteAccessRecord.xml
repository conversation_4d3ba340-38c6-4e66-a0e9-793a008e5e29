<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.dahe.dao.WebsiteAccessRecordDao">


    <select id="listByFilters" resultType="cn.dahe.dto.WebsiteAccessOverviewDto">

        SELECT
            (@row_number := @row_number + 1) AS id,
            w.id AS webId,
            w.web_name AS webName,
            w.web_url AS webUrl,
            wg.group_name AS groupName,
            IFNULL(t.total_count, 0) AS totalCount,
            IFNULL(t.success_count, 0) AS successCount,
            IFNULL(t.error_count, 0) AS errorCount,
            IFNULL(t.success_rate, '0%') AS successRate,
            IFNULL(t.error_rate, '0%') AS errorRate,
            IFNULL(t.avg_response_time, 0) AS avgResponseTime,
            t.last_check_time AS lastCheckTime,
            CASE WHEN IFNULL(t.error_count, 0) = 0 THEN 1 ELSE 0 END AS status
        FROM t_website w
        LEFT JOIN t_website_group wg ON w.group_id = wg.id
        CROSS JOIN (SELECT @row_number := 0) r
        LEFT JOIN (
            SELECT
                web_id,
                COUNT(*) AS total_count,
                SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) AS success_count,
                SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) AS error_count,
                CONCAT(ROUND(SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2), '%') AS success_rate,
                CONCAT(ROUND(SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2), '%') AS error_rate,
                AVG(access_time_consuming) AS avg_response_time,
                MAX(check_time) AS last_check_time
            FROM t_website_access_record
            WHERE 1=1
        <if test="beginTime !=null and beginTime !='' ">
            AND check_time &gt;= #{beginTime}
        </if>
        <if test="endTime !=null and endTime !='' ">
            AND check_time &lt;= #{endTime}
        </if>
        GROUP BY web_id
        ) t ON w.id = t.web_id
        WHERE 1 = 1
        <if test="webIdList != null and webIdList.size > 0">
            AND w.id IN
            <foreach collection="webIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="groupType !=null and groupType !='' ">
            AND wg.group_name = #{groupType}
        </if>
        ORDER BY w.id
    </select>
</mapper>