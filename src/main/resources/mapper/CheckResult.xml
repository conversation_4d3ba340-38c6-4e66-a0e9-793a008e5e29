<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dahe.dao.CheckResultDao">




    <sql id="resultAuditCondition">
        <if test="query.resultAuditStatuses != null and query.resultAuditStatuses.size() > 0">
            AND tcr.audit_status IN
            <foreach collection="query.resultAuditStatuses" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
    </sql>
    <sql id="wordIdCondition">
        <if test="query.wordId != null">
            AND tcr.word_id = #{query.wordId}
        </if>
    </sql>




    <!-- 获取文章的错误信息 -->
    <select id="listCheckResultByTaskId" resultType="cn.dahe.model.vo.CheckResultVO">
        SELECT
        tcr.id as result_id,
        tcw.error_word,
        tcr.html_error_word,
        tcw.suggest_word,
        tcr.position,
        tcr.html_position,
        ifNULl(tcw.third_error_type,tcw.second_error_type) as error_type,
        tcr.article_location,
        tcw.error_level,
        tcr.audit_status,
        tcr.context,
        tcr.marked_context,
        tcw.filter_status
        FROM t_check_result tcr
        left join t_check_word tcw on tcr.word_id = tcw.id
        left join t_check_task tct on tcr.check_id = tct.id
        WHERE tct.id = #{taskId}
        <include refid="resultAuditCondition"/>
        <include refid="cn.dahe.dao.CheckWordDao.filterStatusCondition"/>
        <include refid="cn.dahe.dao.CheckWordDao.wordCondition"/>
        <if test="query.onlyShowSpecifiedErrors != null and query.onlyShowSpecifiedErrors">
            <include refid="cn.dahe.dao.CheckWordDao.errorTypeAndLevelCondition"/>
            <include refid="cn.dahe.dao.CheckResultDao.wordIdCondition"/>
        </if>
        order by tcr.article_location desc,tcr.position
    </select>


</mapper>