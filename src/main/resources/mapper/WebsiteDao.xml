<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.dahe.dao.WebsiteDao">

    <sql id="websiteCondition">
        <if test="query.websiteIds != null and query.websiteIds.size() > 0">
            AND tw.id IN
            <foreach collection="query.websiteIds" item="websiteId" open="(" separator="," close=")">
                #{websiteId}
            </foreach>
        </if>
    </sql>

    <select id="listTotal" resultType="cn.dahe.model.vo.WebsiteVO">
        SELECT w.id,
               w.web_name       as webName,
               w.web_url        as webUrl,
               w.web_code_id    as webCodeId,
               w.group_id       as groupId,
               w.check_strategy as checkStrategy
        FROM t_website w
        WHERE w.status = 1
          AND w.is_del = 0
        ORDER BY w.create_time DESC
    </select>

</mapper> 