<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.dahe.dao.RoleDao">

    <resultMap type="cn.dahe.entity.Role" id="roleMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="sn" column="sn"/>
        <result property="note" column="note"/>
        <result property="status" column="status"/>
        <collection property="permissionArrayList" ofType="cn.dahe.entity.Permission">
            <result property="id" column="p_id"/>
            <result property="name" column="p_name"/>
            <result property="sn" column="p_sn"/>
            <result property="note" column="p_note"/>
            <result property="status" column="p_status"/>
            <result property="pid" column="p_pid"/>
            <result property="type" column="p_type"/>
        </collection>
    </resultMap>
    <select id="listByFilters" resultMap="roleMap">
        select r.*,
        p.`id` p_id,
        p.`name` p_name,
        p.`sn` p_sn,
        p.`note` p_note,
        p.`status` p_status,
        p.`pid` p_pid,
        p.`type` p_type
        from t_role r
        left join t_permission_role pr
        on r.`id` = pr.`role_id`
        left join t_permission p
        on pr.`permission_id` = p.`id`
        where 1 = 1
        <if test="permissionStatus!=null and permissionStatus!=''">
            and p.`status` = #{permissionStatus}
        </if>
        <if test="roleIds!=null and roleIds.size>0">
            and r.`id` in
            <foreach collection="roleIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="roleSns!=null and roleSns.size>0">
            and r.`sn` in
            <foreach collection="roleSns" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>