<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dahe.dao.WebsiteReprintSourceDao">

    <sql id="filterStatusCondition">
        <if test="query.filterStatuses != null and query.filterStatuses.size() > 0">
            AND twrs.filter_status IN
            <foreach collection="query.filterStatuses" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
    </sql>
    <sql id="filterDateCondition">
        <if test="query.filterBeginDate != null">
            AND twrs.pub_time >= DATE_FORMAT(#{query.filterBeginDate}, '%Y-%m-%d 00:00:00')
        </if>
        <if test="query.filterEndDate != null">
            AND twrs.pub_time &lt;= DATE_FORMAT(#{query.filterEndDate}, '%Y-%m-%d 23:59:59')
        </if>
    </sql>
    <sql id="reprintSourceCondition">
        <if test="query.reprintSource != null">
            and twrs.reprint_source LIKE CONCAT('%', #{query.reprintSource}, '%')
        </if>
    </sql>

    <!-- 分页查询转载信源列表 -->
    <select id="pageList" resultType="cn.dahe.model.vo.WebsiteReprintSourceVO">
        SELECT
        twrs.id,
        twrs.website_id,
        tw.web_name as website_name,
        twrs.reprint_source,
        twrs.filter_status,
        twrs.filter_time
        FROM t_website_reprint_source twrs
        LEFT JOIN t_website tw ON tw.id = twrs.website_id
        <where>
            <include refid="cn.dahe.dao.WebsiteDao.websiteCondition"/>
            <include refid="filterStatusCondition"/>
            <include refid="reprintSourceCondition"/>
            <include refid="filterDateCondition"/>
        </where>
        ORDER BY twrs.filter_status desc, twrs.filter_time DESC
    </select>

</mapper>
