<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.dahe.dao.ChkAllSiteSearchDao">

    <resultMap id="ChkAllSiteSearchVOMap" type="cn.dahe.model.vo.ChkAllSiteSearchVO">
        <id property="id" column="id"/>
        <result property="websiteName" column="website_name"/>
        <result property="groupName" column="group_name"/>
        <result property="title" column="title"/>
        <result property="contentSummary" column="content_summary"/>
        <result property="content" column="content"/>
        <result property="url" column="url"/>
        <result property="publishTime" column="publish_time"/>
        <result property="contentType" column="content_type"/>
        <result property="contentTypeName" column="content_type_name"/>
        <result property="isFiltered" column="is_filtered"/>
        <result property="filterStatusName" column="filter_status_name"/>
        <result property="sourceWebsite" column="source_website"/>
        <result property="attachmentName" column="attachment_name"/>
        <result property="attachmentUrl" column="attachment_url"/>
        <result property="attachmentSize" column="attachment_size"/>
        <result property="matchRate" column="match_rate"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <!-- ==================== 全站搜索记录 ==================== -->
    
    <select id="selectPageWithExtInfo" resultMap="ChkAllSiteSearchVOMap">
        SELECT 
            cass.id,
            w.web_name as website_name,
            wg.group_name as group_name,
            cass.title,
            CASE 
                WHEN LENGTH(cass.content) > 100 THEN CONCAT(SUBSTRING(cass.content, 1, 100), '...')
                ELSE cass.content
            END as content_summary,
            cass.content,
            cass.url,
            cass.publish_time,
            cass.content_type,
            CASE 
                WHEN cass.content_type = 1 THEN '文章'
                WHEN cass.content_type = 2 THEN '附件'
                ELSE '未知'
            END as content_type_name,
            cass.is_filtered,
            CASE 
                WHEN cass.is_filtered = 0 THEN '未过滤'
                WHEN cass.is_filtered = 1 THEN '已过滤转载信源'
                ELSE '未知'
            END as filter_status_name,
            cass.source_website,
            cass.attachment_name,
            cass.attachment_url,
            cass.attachment_size,
            '85%' as match_rate,
            cass.create_time
        FROM chk_all_site_search cass
        LEFT JOIN t_website w ON cass.website_id = w.id
        LEFT JOIN t_website_group wg ON cass.group_id = wg.id
        WHERE cass.is_del = 0
        
        <!-- 站点类型和站点值筛选 -->
        <if test="query.siteType == 1 and query.siteValue != null and query.siteValue != ''">
            <!-- 按网站筛选 -->
            AND w.web_name LIKE CONCAT('%', #{query.siteValue}, '%')
        </if>
        <if test="query.siteType == 2 and query.siteValue != null and query.siteValue != ''">
            <!-- 按分组筛选 -->
            AND wg.group_name LIKE CONCAT('%', #{query.siteValue}, '%')
        </if>
        
        <!-- 关键词搜索 -->
        <if test="query.keywords != null and query.keywords != ''">
            <choose>
                <when test="query.keywordsContentType == 1">
                    <!-- 仅标题搜索 -->
                    AND cass.title LIKE CONCAT('%', #{query.keywords}, '%')
                </when>
                <when test="query.keywordsContentType == 2">
                    <!-- 标题和内容搜索 -->
                    AND (cass.title LIKE CONCAT('%', #{query.keywords}, '%') 
                         OR cass.content LIKE CONCAT('%', #{query.keywords}, '%'))
                </when>
                <otherwise>
                    <!-- 默认标题和内容搜索 -->
                    AND (cass.title LIKE CONCAT('%', #{query.keywords}, '%') 
                         OR cass.content LIKE CONCAT('%', #{query.keywords}, '%'))
                </otherwise>
            </choose>
        </if>
        
        <!-- 内容类型筛选 -->
        <if test="query.contentType == 2">
            <!-- 仅文章 -->
            AND cass.content_type = 1
        </if>
        <if test="query.contentType == 3">
            <!-- 仅附件 -->
            AND cass.content_type = 2
        </if>
        
        <!-- 过滤结果类型筛选 -->
        <if test="query.filterResultType == 2">
            <!-- 未过滤 -->
            AND cass.is_filtered = 0
        </if>
        <if test="query.filterResultType == 3">
            <!-- 已过滤转载信源 -->
            AND cass.is_filtered = 1
        </if>
        
        <!-- 发布时间范围筛选 -->
        <if test="query.startTime != null and query.startTime != ''">
            AND cass.publish_time &gt;= #{query.startTime}
        </if>
        <if test="query.endTime != null and query.endTime != ''">
            AND cass.publish_time &lt;= #{query.endTime}
        </if>
        
        <!-- 排序 -->
        <choose>
            <when test="query.orderValue == 'asc'">
                ORDER BY cass.publish_time ASC
            </when>
            <otherwise>
                ORDER BY cass.publish_time DESC
            </otherwise>
        </choose>
    </select>

</mapper>
