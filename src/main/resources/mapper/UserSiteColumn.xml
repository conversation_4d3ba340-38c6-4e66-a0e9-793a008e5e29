<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.dahe.dao.UserSiteColumnDao">

    <!-- 根据用户ID和站点ID查询栏目列表 -->
    <select id="selectByUserIdAndSiteId" resultType="cn.dahe.entity.UserSiteColumn">
        SELECT
            id,
            user_id,
            user_name,
            site_id,
            site_name,
            site_url,
            column_id,
            column_name,
            column_url,
            create_time,
            create_by_user_id,
            create_by_user_name,
            update_time,
            update_by_user_id,
            update_by_user_name
        FROM t_user_site_column
        WHERE user_id = #{userId} AND site_id = #{siteId}
        ORDER BY id DESC
    </select>

    <!-- 根据用户ID查询所有分配的站点栏目 -->
    <select id="selectByUserId" resultType="cn.dahe.entity.UserSiteColumn">
        SELECT
            id,
            user_id,
            user_name,
            site_id,
            site_name,
            site_url,
            column_id,
            column_name,
            column_url,
            create_time,
            create_by_user_id,
            create_by_user_name,
            update_time,
            update_by_user_id,
            update_by_user_name
        FROM t_user_site_column
        WHERE user_id = #{userId}
        ORDER BY site_id, column_id
    </select>

    <!-- 检查用户、站点、栏目组合是否已存在 -->
    <select id="countByUserSiteColumn" resultType="int">
        SELECT COUNT(1)
        FROM t_user_site_column
        WHERE user_id = #{userId} 
          AND site_id = #{siteId} 
          AND column_id = #{columnId}
    </select>

    <!-- 批量删除用户的站点栏目分配 -->
    <delete id="deleteByUserIdAndSiteIds">
        DELETE FROM t_user_site_column
        WHERE user_id = #{userId}
        <if test="siteIds != null and siteIds.size() > 0">
            AND site_id IN
            <foreach collection="siteIds" item="siteId" open="(" separator="," close=")">
                #{siteId}
            </foreach>
        </if>
    </delete>

    <!-- 批量删除用户的特定站点栏目分配 -->
    <delete id="deleteByUserIdAndSiteIdAndColumnIds">
        DELETE FROM t_user_site_column
        WHERE user_id = #{userId} 
          AND site_id = #{siteId}
        <if test="columnIds != null and columnIds.size() > 0">
            AND column_id IN
            <foreach collection="columnIds" item="columnId" open="(" separator="," close=")">
                #{columnId}
            </foreach>
        </if>
    </delete>

</mapper>
