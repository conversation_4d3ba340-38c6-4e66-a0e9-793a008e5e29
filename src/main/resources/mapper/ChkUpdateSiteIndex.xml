<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.dahe.dao.ChkUpdateSiteIndexDao">

    <resultMap id="ChkUpdateSiteIndexDTOMap" type="java.util.HashMap">
        <result property="id" column="id"/>
        <result property="groupName" column="group_name"/>
        <result property="websiteName" column="website_name"/>
        <result property="websiteIndexUrl" column="website_index_url"/>
        <result property="status" column="status"/>
        <result property="updateDaysStr" column="update_days"/>
        <result property="continuousNotUpdateDaysStr" column="continuous_not_update_days"/>
        <result property="lastParseTime" column="parse_time"/>
    </resultMap>

    <!-- ==================== 首页更新检查概览 ==================== -->

    <select id="getOverviewStatistics" resultType="map">
        SELECT
            COALESCE(
                (SELECT COUNT(DISTINCT website_id) FROM chk_update_site_index cusi WHERE 1=1
                <if test="query.groupId != null and query.groupId != ''">
                    AND FIND_IN_SET(cusi.group_id, #{query.groupId})
                </if>
                <if test="query.groupName != null and query.groupName != ''">
                    AND cusi.group_name REGEXP REPLACE(#{query.groupName}, ',', '|')
                </if>
                <if test="query.websiteId != null and query.websiteId != ''">
                    AND FIND_IN_SET(cusi.website_id, #{query.websiteId})
                </if>
                <if test="query.websiteName != null and query.websiteName != ''">
                    AND cusi.website_name REGEXP REPLACE(#{query.websiteName}, ',', '|')
                </if>
                <if test="query.websiteIndexUrl != null and query.websiteIndexUrl != ''">
                    AND cusi.website_index_url REGEXP REPLACE(#{query.websiteIndexUrl}, ',', '|')
                </if>
                ),
                0
            ) as totalWebsiteCount,
            COALESCE(
                (SELECT COUNT(DISTINCT website_id) FROM chk_update_site_index cusi WHERE article_publish_time &gt;= DATE_SUB(NOW(), INTERVAL 1 DAY)
                <if test="query.groupId != null and query.groupId != ''">
                    AND FIND_IN_SET(cusi.group_id, #{query.groupId})
                </if>
                <if test="query.groupName != null and query.groupName != ''">
                    AND cusi.group_name REGEXP REPLACE(#{query.groupName}, ',', '|')
                </if>
                <if test="query.websiteId != null and query.websiteId != ''">
                    AND FIND_IN_SET(cusi.website_id, #{query.websiteId})
                </if>
                <if test="query.websiteName != null and query.websiteName != ''">
                    AND cusi.website_name REGEXP REPLACE(#{query.websiteName}, ',', '|')
                </if>
                <if test="query.websiteIndexUrl != null and query.websiteIndexUrl != ''">
                    AND cusi.website_index_url REGEXP REPLACE(#{query.websiteIndexUrl}, ',', '|')
                </if>
                ),
                0
            ) as updatedWebsiteCount,
            COALESCE(
                (SELECT COUNT(DISTINCT website_id) FROM chk_update_site_index cusi WHERE website_id NOT IN
                    (SELECT DISTINCT website_id FROM chk_update_site_index WHERE article_publish_time &gt;= DATE_SUB(NOW(), INTERVAL 1 DAY))
                <if test="query.groupId != null and query.groupId != ''">
                    AND FIND_IN_SET(cusi.group_id, #{query.groupId})
                </if>
                <if test="query.groupName != null and query.groupName != ''">
                    AND (
                    <foreach collection="query.groupName.split(',')" item="name" separator=" OR ">
                        cusi.group_name LIKE CONCAT('%', #{name}, '%')
                    </foreach>
                    )
                </if>
                <if test="query.websiteId != null and query.websiteId != ''">
                    AND FIND_IN_SET(cusi.website_id, #{query.websiteId})
                </if>
                <if test="query.websiteName != null and query.websiteName != ''">
                    AND cusi.website_name REGEXP REPLACE(#{query.websiteName}, ',', '|')
                </if>
                <if test="query.websiteIndexUrl != null and query.websiteIndexUrl != ''">
                    AND cusi.website_index_url REGEXP REPLACE(#{query.websiteIndexUrl}, ',', '|')
                </if>
                ),
                0
            ) as notUpdatedWebsiteCount
    </select>

    <!-- ==================== 首页更新检查记录 ==================== -->

    <select id="selectPageWithExtInfo" resultMap="ChkUpdateSiteIndexDTOMap">
        SELECT
            cusi.website_id as id,
            cusi.group_name,
            cusi.website_name,
            cusi.website_index_url,
            CASE
                WHEN MAX(cusi.article_publish_time) &gt;= DATE_SUB(NOW(), INTERVAL 1 DAY) THEN 1
                ELSE 0
            END as status,
            GROUP_CONCAT(DISTINCT DATE_FORMAT(cusi.article_publish_time, '%Y-%m-%d') ORDER BY cusi.article_publish_time DESC SEPARATOR ',') as update_days,
            CASE
                WHEN MAX(cusi.article_publish_time) &gt;= DATE_SUB(NOW(), INTERVAL 1 DAY) THEN ''
                ELSE CONCAT(DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 DAY), '%Y-%m-%d'), ',', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 2 DAY), '%Y-%m-%d'))
            END as continuous_not_update_days,
            MAX(cusi.parse_time) as parse_time
        FROM chk_update_site_index cusi
        WHERE 1=1
        <if test="groupId != null and groupId != ''">
            AND FIND_IN_SET(cusi.group_id, #{groupId})
        </if>
        <if test="groupName != null and groupName != ''">
            AND (
                cusi.group_name REGEXP REPLACE(#{groupName}, ',', '|')
            )
        </if>
        <if test="websiteId != null and websiteId != ''">
            AND FIND_IN_SET(cusi.website_id, #{websiteId})
        </if>
        <if test="websiteName != null and websiteName != ''">
            AND (
                cusi.website_name REGEXP REPLACE(#{websiteName}, ',', '|')
            )
        </if>
        <if test="websiteIndexUrl != null and websiteIndexUrl != ''">
            AND (
                cusi.website_index_url REGEXP REPLACE(#{websiteIndexUrl}, ',', '|')
            )
        </if>
        <if test="beginTime != null and beginTime != ''">
            AND cusi.parse_time &gt;= #{beginTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND cusi.parse_time &lt;= #{endTime}
        </if>
        GROUP BY cusi.website_id, cusi.group_name, cusi.website_name, cusi.website_index_url
        ORDER BY MAX(cusi.create_time) DESC
    </select>

    <select id="selectDetailById" resultMap="ChkUpdateSiteIndexDTOMap">
        SELECT
            cusi.website_id as id,
            cusi.group_name,
            cusi.website_name,
            cusi.website_index_url,
            CASE
                WHEN MAX(cusi.article_publish_time) &gt;= DATE_SUB(NOW(), INTERVAL 1 DAY) THEN 1
                ELSE 0
            END as status,
            GROUP_CONCAT(DISTINCT DATE_FORMAT(cusi.article_publish_time, '%Y-%m-%d') ORDER BY cusi.article_publish_time DESC SEPARATOR ',') as update_days,
            CASE
                WHEN MAX(cusi.article_publish_time) &gt;= DATE_SUB(NOW(), INTERVAL 1 DAY) THEN ''
                ELSE CONCAT(DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 DAY), '%Y-%m-%d'), ',', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 2 DAY), '%Y-%m-%d'))
            END as continuous_not_update_days,
            MAX(cusi.parse_time) as parse_time
        FROM chk_update_site_index cusi
        WHERE cusi.website_id = #{id}
        GROUP BY cusi.website_id, cusi.group_name, cusi.website_name, cusi.website_index_url
    </select>

    <!-- ==================== 详细记录分页查询 ==================== -->

    <select id="selectDetailPage" resultType="cn.dahe.entity.ChkUpdateSiteIndex">
        SELECT
            cusi.id,
            cusi.group_id,
            cusi.group_name,
            cusi.website_id,
            cusi.website_name,
            cusi.website_index_url,
            cusi.article_title,
            cusi.article_content,
            cusi.article_url,
            cusi.article_publish_time,
            cusi.parse_time,
            cusi.create_time
        FROM chk_update_site_index cusi
        WHERE 1=1
        <if test="groupId != null and groupId != ''">
            AND FIND_IN_SET(cusi.group_id, #{groupId})
        </if>
        <if test="groupName != null and groupName != ''">
            AND cusi.group_name REGEXP REPLACE(#{groupName}, ',', '|')
        </if>
        <if test="websiteId != null and websiteId != ''">
            AND FIND_IN_SET(cusi.website_id, #{websiteId})
        </if>
        <if test="websiteName != null and websiteName != ''">
            AND cusi.website_name REGEXP REPLACE(#{websiteName}, ',', '|')
        </if>
        <if test="websiteIndexUrl != null and websiteIndexUrl != ''">
            AND cusi.website_index_url REGEXP REPLACE(#{websiteIndexUrl}, ',', '|')
        </if>
        <if test="beginTime != null and beginTime != ''">
            AND cusi.parse_time &gt;= #{beginTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND cusi.parse_time &lt;= #{endTime}
        </if>
        ORDER BY cusi.create_time DESC
    </select>

</mapper>
