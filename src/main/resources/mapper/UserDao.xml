<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.dahe.dao.UserDao">

    <resultMap type="cn.dahe.entity.User" id="userMap">
        <result property="userId" column="user_id"/>
        <result property="cityId" column="city_id"/>
        <result property="userName" column="user_name"/>
        <result property="phone" column="phone"/>
    </resultMap>
    <select id="listByFilters" resultType="cn.dahe.entity.User">
        SELECT u.*
        FROM t_role r
        LEFT JOIN t_user_role ur
        ON r.id=ur.role_id
        LEFT JOIN t_user u
        ON ur.user_id=u.user_id
        WHERE 1=1
        <if test="roleId!=null and roleId!=''">
            AND r.id=#{roleId}
        </if>
        <if test="userName!=null and userName!=''">
            AND u.user_name=#{userName}
        </if>
        <if test="depIdList!=null and depIdList.size>0">
            AND u.dep_id in
            <foreach collection="depIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </select>
    <select id="listByRoleIdAndUserName" resultType="cn.dahe.entity.User">

        SELECT *
        FROM t_user u
        LEFT JOIN t_user_role ur
        ON u.`user_id`=ur.`user_id`
        WHERE 1=1
        <if test="roleId!=null and roleId!=''">
            AND ur.`role_id`=#{roleId}
        </if>
        <if test="userName!=null and userName!=''">
            AND u.user_name LIKE CONCAT('%', #{userName}, '%')
        </if>

    </select>

</mapper>