spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: *************************************************************************************************
      username: axxm
      password: Sd2of8aJ2KFUkd84lxa
      filters: stat,wall
      max-active: 100
      initial-size: 10
      max-wait: 60000
      min-idle: 1
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: select '1'
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-open-prepared-statements: 50
      max-pool-prepared-statement-per-connection-size: 20

  redis:
    host: ************
    port: 6418
    password: Dlsielxiec82lfslfulssxx


knife4j:
  # 关闭生产环境接口文档
  production: true
